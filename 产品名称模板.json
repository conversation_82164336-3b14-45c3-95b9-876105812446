{"产品清单模板": {"说明": "此文件为合同自动生成系统的产品名称标准格式模板", "版本": "1.0", "创建日期": "2025-01-18", "使用说明": ["1. 系统将根据此模板格式识别和导入产品信息", "2. 产品名称必须与产品规格书文件夹名称完全一致", "3. 价格单位为人民币元", "4. 数量默认为1，可在系统中修改", "5. 系统会自动计算总金额并应用相应的支付方式"], "产品列表": [{"产品名称": "1200半自动印刷机", "产品型号": "NEODEN-1200", "单价": 45000, "默认数量": 1, "产品类别": "印刷设备", "规格书路径": "产品规格书/1200半自动印刷机", "描述": "1200mm半自动印刷机，适用于中大型PCB印刷"}, {"产品名称": "350半自动印刷机", "产品型号": "NEODEN-350", "单价": 28000, "默认数量": 1, "产品类别": "印刷设备", "规格书路径": "产品规格书/350半自动印刷机", "描述": "350mm半自动印刷机，适用于小型PCB印刷"}, {"产品名称": "600半自动印刷机", "产品型号": "NEODEN-600", "单价": 35000, "默认数量": 1, "产品类别": "印刷设备", "规格书路径": "产品规格书/600半自动印刷机", "描述": "600mm半自动印刷机，适用于中型PCB印刷"}, {"产品名称": "ND1全自动印刷机", "产品型号": "NEODEN-ND1", "单价": 85000, "默认数量": 1, "产品类别": "印刷设备", "规格书路径": "产品规格书/ND1全自动印刷机", "描述": "ND1全自动印刷机，高精度全自动印刷解决方案"}, {"产品名称": "ND2全自动印刷机", "产品型号": "NEODEN-ND2", "单价": 120000, "默认数量": 1, "产品类别": "印刷设备", "规格书路径": "产品规格书/ND2全自动印刷机", "描述": "ND2全自动印刷机，高端全自动印刷解决方案"}, {"产品名称": "4代贴片机", "产品型号": "NEODEN-4", "单价": 65000, "默认数量": 1, "产品类别": "贴片设备", "规格书路径": "产品规格书/4代贴片机", "描述": "第四代贴片机，高精度元件贴装设备"}, {"产品名称": "9代贴片机", "产品型号": "NEODEN-9", "单价": 95000, "默认数量": 1, "产品类别": "贴片设备", "规格书路径": "产品规格书/9代贴片机", "描述": "第九代贴片机，高速高精度贴装设备"}, {"产品名称": "9代贴片机-LINE", "产品型号": "NEODEN-9-LINE", "单价": 150000, "默认数量": 1, "产品类别": "贴片设备", "规格书路径": "产品规格书/9代贴片机-LINE", "描述": "第九代贴片机生产线版本，适用于大批量生产"}, {"产品名称": "N10P贴片机", "产品型号": "NEODEN-N10P", "单价": 180000, "默认数量": 1, "产品类别": "贴片设备", "规格书路径": "产品规格书/N10P贴片机", "描述": "N10P高端贴片机，专业级贴装解决方案"}, {"产品名称": "N10P贴片机-LINE", "产品型号": "NEODEN-N10P-LINE", "单价": 250000, "默认数量": 1, "产品类别": "贴片设备", "规格书路径": "产品规格书/N10P贴片机-LINE", "描述": "N10P贴片机生产线版本，高端大批量生产解决方案"}, {"产品名称": "YY1桌面贴片机", "产品型号": "NEODEN-YY1", "单价": 25000, "默认数量": 1, "产品类别": "贴片设备", "规格书路径": "产品规格书/YY1桌面贴片机", "描述": "YY1桌面型贴片机，适用于小批量和原型制作"}, {"产品名称": "IN6回流焊", "产品型号": "NEODEN-IN6", "单价": 42000, "默认数量": 1, "产品类别": "回流焊设备", "规格书路径": "产品规格书/IN6回流焊", "描述": "IN6回流焊炉，6温区回流焊解决方案"}, {"产品名称": "IN6C回流焊", "产品型号": "NEODEN-IN6C", "单价": 48000, "默认数量": 1, "产品类别": "回流焊设备", "规格书路径": "产品规格书/IN6C回流焊", "描述": "IN6C回流焊炉，6温区强制对流回流焊"}, {"产品名称": "IN8C回流焊", "产品型号": "NEODEN-IN8C", "单价": 65000, "默认数量": 1, "产品类别": "回流焊设备", "规格书路径": "产品规格书/IN8C回流焊", "描述": "IN8C回流焊炉，8温区强制对流回流焊"}, {"产品名称": "IN12回流焊", "产品型号": "NEODEN-IN12", "单价": 85000, "默认数量": 1, "产品类别": "回流焊设备", "规格书路径": "产品规格书/IN12回流焊", "描述": "IN12回流焊炉，12温区高精度回流焊"}, {"产品名称": "IN12C回流焊", "产品型号": "NEODEN-IN12C", "单价": 95000, "默认数量": 1, "产品类别": "回流焊设备", "规格书路径": "产品规格书/IN12C回流焊", "描述": "IN12C回流焊炉，12温区强制对流回流焊"}, {"产品名称": "有框印刷台", "产品型号": "NEODEN-FRAME", "单价": 8000, "默认数量": 1, "产品类别": "印刷配件", "规格书路径": "产品规格书/有框印刷台", "描述": "有框印刷台，适用于标准PCB印刷"}, {"产品名称": "无框印刷台", "产品型号": "NEODEN-FRAMELESS", "单价": 12000, "默认数量": 1, "产品类别": "印刷配件", "规格书路径": "产品规格书/无框印刷台", "描述": "无框印刷台，适用于异形PCB印刷"}], "导入格式说明": {"支持格式": ["JSON", "CSV", "Excel"], "必填字段": ["产品名称", "单价"], "可选字段": ["产品型号", "默认数量", "产品类别", "描述"], "注意事项": ["产品名称必须与产品规格书文件夹名称完全匹配", "单价必须为正数", "数量默认为1，系统中可修改", "产品类别用于分类显示和统计"]}, "系统集成说明": {"自动识别规则": ["系统根据产品名称自动匹配产品规格书文件夹", "自动计算总金额并应用支付方式规则", "超过100000元自动选择分期付款方式", "低于100000元自动选择全款付款方式"], "合同生成流程": ["1. 选择产品并设置数量", "2. 系统自动计算总金额", "3. 根据金额自动选择支付方式", "4. 生成合同并附加产品规格书", "5. 支持PDF导出和历史记录保存"]}}}