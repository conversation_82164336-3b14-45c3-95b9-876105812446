# 合同自动生成系统 - 产品名称文件格式规范

## 概述
本文档定义了合同自动生成系统中产品名称的标准文件格式，确保系统能够正确识别和导入产品信息。

## 支持的文件格式

### 1. JSON格式 (推荐)
- 文件名：`产品名称模板.json`
- 编码：UTF-8
- 结构化数据，支持完整的产品信息

### 2. CSV格式 (简化版)
- 文件名：`产品清单模板.csv`
- 编码：UTF-8
- 表格格式，便于Excel编辑

### 3. Excel格式 (待开发)
- 文件扩展名：`.xlsx` 或 `.xls`
- 支持多工作表

## 数据字段规范

### 必填字段
1. **产品名称** (product_name)
   - 类型：字符串
   - 要求：必须与`产品规格书`文件夹中的产品文件夹名称完全一致
   - 示例：`"1200半自动印刷机"`

2. **单价** (unit_price)
   - 类型：数字
   - 单位：人民币元
   - 要求：必须为正数
   - 示例：`45000`

### 可选字段
1. **产品型号** (model)
   - 类型：字符串
   - 示例：`"NEODEN-1200"`

2. **默认数量** (default_quantity)
   - 类型：整数
   - 默认值：1
   - 示例：`1`

3. **产品类别** (category)
   - 类型：字符串
   - 用途：分类显示和统计
   - 示例：`"印刷设备"`

4. **描述** (description)
   - 类型：字符串
   - 用途：产品详细说明
   - 示例：`"1200mm半自动印刷机，适用于中大型PCB印刷"`

## 系统识别规则

### 产品名称匹配规则
1. 系统根据产品名称在`产品规格书`目录下查找对应的文件夹
2. 产品名称必须与文件夹名称完全匹配（区分大小写）
3. 如果找不到对应的规格书文件夹，系统会提示警告但仍可继续

### 价格计算规则
1. 总金额 = 单价 × 数量
2. 系统自动计算各种比例金额（30%、60%、10%）
3. 自动转换大写金额

### 支付方式自动选择规则
- **总金额 ≥ 100,000元**：自动选择分期付款方式
  - 预付定金：30%
  - 发货款：60%
  - 收货尾款：10%
- **总金额 < 100,000元**：自动选择全款付款方式

## 文件导入流程

### 1. 文件验证
- 检查文件格式是否支持
- 验证文件编码（必须为UTF-8）
- 检查必填字段是否完整

### 2. 数据验证
- 验证产品名称格式
- 检查单价是否为正数
- 验证数量是否为正整数

### 3. 规格书匹配
- 根据产品名称查找对应的规格书文件夹
- 验证规格书文件是否存在
- 记录匹配结果

### 4. 数据导入
- 将验证通过的产品信息导入系统
- 生成导入报告
- 显示匹配失败的产品列表

## 错误处理

### 常见错误类型
1. **产品名称不匹配**
   - 错误信息：找不到对应的产品规格书
   - 解决方案：检查产品名称是否与文件夹名称完全一致

2. **价格格式错误**
   - 错误信息：单价必须为正数
   - 解决方案：检查价格字段是否为有效数字

3. **文件编码错误**
   - 错误信息：文件编码不支持
   - 解决方案：将文件保存为UTF-8编码

4. **必填字段缺失**
   - 错误信息：缺少必填字段
   - 解决方案：补充产品名称和单价信息

## 最佳实践

### 1. 文件命名
- 使用描述性的文件名
- 避免使用特殊字符
- 建议使用日期标识版本

### 2. 数据维护
- 定期更新产品价格
- 保持产品名称与规格书文件夹同步
- 及时删除停产产品

### 3. 备份策略
- 定期备份产品数据文件
- 保留历史版本用于追溯
- 建立数据恢复机制

## 示例文件

### JSON格式示例
```json
{
  "产品列表": [
    {
      "产品名称": "1200半自动印刷机",
      "产品型号": "NEODEN-1200",
      "单价": 45000,
      "默认数量": 1,
      "产品类别": "印刷设备",
      "描述": "1200mm半自动印刷机，适用于中大型PCB印刷"
    }
  ]
}
```

### CSV格式示例
```csv
产品名称,产品型号,单价,默认数量,产品类别,描述
1200半自动印刷机,NEODEN-1200,45000,1,印刷设备,1200mm半自动印刷机，适用于中大型PCB印刷
```

## 技术支持

如有问题，请联系系统管理员或参考系统帮助文档。

---
*文档版本：1.0*  
*最后更新：2025-01-18*
