# 产品规格书文件夹说明

## 文件夹结构

```
产品规格书/
├── 智能控制主机A型/
│   ├── 产品参数表.jpg
│   ├── 技术规格书.png
│   ├── 产品图片.jpg
│   └── 安装说明.png
├── 智能控制主机B型/
│   ├── 产品参数表.jpg
│   ├── 技术规格书.png
│   ├── 产品图片.jpg
│   └── 安装说明.png
└── 工业级控制器/
    ├── 产品参数表.jpg
    ├── 技术规格书.png
    ├── 产品图片.jpg
    └── 安装说明.png
```

## 使用说明

### 1. 文件夹命名
- 每个产品的文件夹名称必须与产品数据中的产品名称完全一致
- 例如：产品名称为"智能控制主机A型"，则文件夹名称也必须是"智能控制主机A型"

### 2. 支持的文件格式
- JPG (.jpg)
- PNG (.png)
- JPEG (.jpeg)

### 3. 推荐的文件名
- **产品参数表**: 产品参数表.jpg / 产品参数表.png
- **技术规格书**: 技术规格书.jpg / 技术规格书.png
- **产品图片**: 产品图片.jpg / 产品图片.png
- **安装说明**: 安装说明.jpg / 安装说明.png

### 4. 在报价单中使用
1. 在"生成报价单"页面选择需要的产品
2. 勾选"附加产品规格书页面"选项
3. 生成报价单时，系统会自动为选中的产品附加对应的规格书页面
4. 每个产品的每种规格书类型都会生成独立的A4页面

### 5. 注意事项
- 由于浏览器安全限制，直接打开HTML文件可能无法显示本地图片
- 建议使用本地服务器运行，或将图片转换为Base64格式嵌入
- 确保图片文件路径和名称正确，区分大小写
- 建议使用清晰的A4尺寸图片，以获得最佳打印效果

### 6. 文件管理
- 可以为每个产品添加多种类型的规格书
- 不需要的规格书类型可以不添加文件
- 系统会自动跳过不存在的文件

## 示例
如果您有一个名为"智能控制主机A型"的产品，您需要：
1. 创建文件夹：`产品规格书/智能控制主机A型/`
2. 在该文件夹中放置规格书图片文件
3. 在报价单生成时勾选"附加产品规格书页面"
4. 系统会自动为该产品生成规格书页面
