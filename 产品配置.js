// 合同自动生成系统 - 产品配置文件
// 此文件定义了系统可识别的所有产品信息

const PRODUCT_CONFIG = {
  // 产品数据版本
  version: "1.0",
  lastUpdate: "2025-01-18",
  
  // 产品列表
  products: {
    // 印刷设备类
    "1200半自动印刷机": {
      model: "NEODEN-1200",
      price: 45000,
      category: "印刷设备",
      specPath: "产品规格书/1200半自动印刷机",
      description: "1200mm半自动印刷机，适用于中大型PCB印刷"
    },
    "350半自动印刷机": {
      model: "NEODEN-350",
      price: 28000,
      category: "印刷设备",
      specPath: "产品规格书/350半自动印刷机",
      description: "350mm半自动印刷机，适用于小型PCB印刷"
    },
    "600半自动印刷机": {
      model: "NEODEN-600",
      price: 35000,
      category: "印刷设备",
      specPath: "产品规格书/600半自动印刷机",
      description: "600mm半自动印刷机，适用于中型PCB印刷"
    },
    "ND1全自动印刷机": {
      model: "NEODEN-ND1",
      price: 85000,
      category: "印刷设备",
      specPath: "产品规格书/ND1全自动印刷机",
      description: "ND1全自动印刷机，高精度全自动印刷解决方案"
    },
    "ND2全自动印刷机": {
      model: "NEODEN-ND2",
      price: 120000,
      category: "印刷设备",
      specPath: "产品规格书/ND2全自动印刷机",
      description: "ND2全自动印刷机，高端全自动印刷解决方案"
    },
    
    // 贴片设备类
    "4代贴片机": {
      model: "NEODEN-4",
      price: 65000,
      category: "贴片设备",
      specPath: "产品规格书/4代贴片机",
      description: "第四代贴片机，高精度元件贴装设备"
    },
    "9代贴片机": {
      model: "NEODEN-9",
      price: 95000,
      category: "贴片设备",
      specPath: "产品规格书/9代贴片机",
      description: "第九代贴片机，高速高精度贴装设备"
    },
    "9代贴片机-LINE": {
      model: "NEODEN-9-LINE",
      price: 150000,
      category: "贴片设备",
      specPath: "产品规格书/9代贴片机-LINE",
      description: "第九代贴片机生产线版本，适用于大批量生产"
    },
    "N10P贴片机": {
      model: "NEODEN-N10P",
      price: 180000,
      category: "贴片设备",
      specPath: "产品规格书/N10P贴片机",
      description: "N10P高端贴片机，专业级贴装解决方案"
    },
    "N10P贴片机-LINE": {
      model: "NEODEN-N10P-LINE",
      price: 250000,
      category: "贴片设备",
      specPath: "产品规格书/N10P贴片机-LINE",
      description: "N10P贴片机生产线版本，高端大批量生产解决方案"
    },
    "YY1桌面贴片机": {
      model: "NEODEN-YY1",
      price: 25000,
      category: "贴片设备",
      specPath: "产品规格书/YY1桌面贴片机",
      description: "YY1桌面型贴片机，适用于小批量和原型制作"
    },
    
    // 回流焊设备类
    "IN6回流焊": {
      model: "NEODEN-IN6",
      price: 42000,
      category: "回流焊设备",
      specPath: "产品规格书/IN6回流焊",
      description: "IN6回流焊炉，6温区回流焊解决方案"
    },
    "IN6C回流焊": {
      model: "NEODEN-IN6C",
      price: 48000,
      category: "回流焊设备",
      specPath: "产品规格书/IN6C回流焊",
      description: "IN6C回流焊炉，6温区强制对流回流焊"
    },
    "IN8C回流焊": {
      model: "NEODEN-IN8C",
      price: 65000,
      category: "回流焊设备",
      specPath: "产品规格书/IN8C回流焊",
      description: "IN8C回流焊炉，8温区强制对流回流焊"
    },
    "IN12回流焊": {
      model: "NEODEN-IN12",
      price: 85000,
      category: "回流焊设备",
      specPath: "产品规格书/IN12回流焊",
      description: "IN12回流焊炉，12温区高精度回流焊"
    },
    "IN12C回流焊": {
      model: "NEODEN-IN12C",
      price: 95000,
      category: "回流焊设备",
      specPath: "产品规格书/IN12C回流焊",
      description: "IN12C回流焊炉，12温区强制对流回流焊"
    },
    
    // 印刷配件类
    "有框印刷台": {
      model: "NEODEN-FRAME",
      price: 8000,
      category: "印刷配件",
      specPath: "产品规格书/有框印刷台",
      description: "有框印刷台，适用于标准PCB印刷"
    },
    "无框印刷台": {
      model: "NEODEN-FRAMELESS",
      price: 12000,
      category: "印刷配件",
      specPath: "产品规格书/无框印刷台",
      description: "无框印刷台，适用于异形PCB印刷"
    }
  },
  
  // 产品类别配置
  categories: {
    "印刷设备": {
      name: "印刷设备",
      description: "PCB印刷相关设备",
      color: "#1890ff"
    },
    "贴片设备": {
      name: "贴片设备", 
      description: "SMT贴片相关设备",
      color: "#52c41a"
    },
    "回流焊设备": {
      name: "回流焊设备",
      description: "回流焊接相关设备", 
      color: "#fa8c16"
    },
    "印刷配件": {
      name: "印刷配件",
      description: "印刷辅助配件",
      color: "#722ed1"
    }
  },
  
  // 系统配置
  systemConfig: {
    // 支付方式阈值
    paymentThreshold: 100000,
    
    // 分期付款比例
    installmentRatio: {
      deposit: 0.30,    // 预付定金 30%
      shipping: 0.60,   // 发货款 60%
      final: 0.10       // 收货尾款 10%
    },
    
    // 税率配置
    taxRates: {
      "浙江登新科技有限公司": 0.13,  // 13%增值税
      "湖州物物通科技有限公司": 0.01   // 1%普通电子发票
    },
    
    // 默认设置
    defaults: {
      quantity: 1,
      currency: "CNY"
    }
  }
};

// 工具函数
const ProductUtils = {
  // 获取产品信息
  getProduct: function(productName) {
    return PRODUCT_CONFIG.products[productName] || null;
  },
  
  // 获取所有产品名称
  getAllProductNames: function() {
    return Object.keys(PRODUCT_CONFIG.products);
  },
  
  // 按类别获取产品
  getProductsByCategory: function(category) {
    const products = {};
    for (const [name, info] of Object.entries(PRODUCT_CONFIG.products)) {
      if (info.category === category) {
        products[name] = info;
      }
    }
    return products;
  },
  
  // 计算总金额
  calculateTotal: function(productName, quantity = 1) {
    const product = this.getProduct(productName);
    if (!product) return 0;
    return product.price * quantity;
  },
  
  // 判断支付方式
  getPaymentMethod: function(totalAmount) {
    return totalAmount >= PRODUCT_CONFIG.systemConfig.paymentThreshold ? 
           'installment' : 'full';
  },
  
  // 计算分期金额
  calculateInstallment: function(totalAmount) {
    const ratios = PRODUCT_CONFIG.systemConfig.installmentRatio;
    return {
      deposit: Math.round(totalAmount * ratios.deposit),
      shipping: Math.round(totalAmount * ratios.shipping), 
      final: Math.round(totalAmount * ratios.final)
    };
  },
  
  // 验证产品名称
  validateProductName: function(productName) {
    return PRODUCT_CONFIG.products.hasOwnProperty(productName);
  },
  
  // 获取规格书路径
  getSpecPath: function(productName) {
    const product = this.getProduct(productName);
    return product ? product.specPath : null;
  }
};

// 导出配置（如果在Node.js环境中）
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    PRODUCT_CONFIG,
    ProductUtils
  };
}
