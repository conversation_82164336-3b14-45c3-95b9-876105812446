# 合同自动生成器 - 启动说明

## 🚀 如何打开系统

### 方法一：直接打开HTML文件（推荐）
1. 找到项目文件夹中的 `index.html` 文件
2. 双击 `index.html` 文件
3. 系统会在默认浏览器中自动打开

### 方法二：通过浏览器打开
1. 打开任意浏览器（Chrome、Firefox、Edge等）
2. 按 `Ctrl + O` 或点击菜单中的"打开文件"
3. 选择项目文件夹中的 `index.html` 文件
4. 点击"打开"

### 方法三：拖拽打开
1. 打开浏览器
2. 将 `index.html` 文件直接拖拽到浏览器窗口中

## 📋 使用步骤

### 1. 选择拟签约公司
- 在左侧第一个区域选择拟签约公司
- 可选择：浙江登新科技有限公司 或 湖州物物通科技有限公司
- 系统会自动显示公司详细信息

### 2. 识别客户信息
- 将客户发送的公司信息粘贴到"客户信息粘贴区"
- 点击"智能识别客户信息"按钮
- 系统会自动识别：
  - 公司全称
  - 纳税人识别号
  - 开户银行
  - 银行账户
  - 联系电话
  - 注册地址

### 3. 识别收件人信息
- 将收件人信息粘贴到"收件人信息粘贴区"
- 点击"智能识别收件信息"按钮
- 系统会自动识别：
  - 收件人姓名
  - 收件人手机号
  - 收件人地址

### 4. 选择产品
- 在产品下拉列表中选择需要的设备
- 点击"添加"按钮将产品加入合同
- 可以调整产品数量
- 系统会自动计算总金额和支付方式

### 5. 预览和导出
- 右侧会实时显示合同预览
- 点击"保存合同"保存到历史记录
- 点击"导出PDF"进行打印或保存

## 💡 功能特色

### 🤖 智能识别
- **客户信息识别**：自动识别公司名称、税号、银行信息等
- **收件人识别**：自动识别姓名、手机号、地址
- **支付方式智能选择**：
  - 总金额 ≥ 10万元：自动选择分期付款（30% + 60% + 10%）
  - 总金额 < 10万元：自动选择全款付款

### 📊 产品管理
- **18种产品**：涵盖印刷设备、贴片设备、回流焊设备、印刷配件
- **实时计算**：自动计算小计、总金额、大写金额
- **数量调整**：支持产品数量的增减和删除

### 📄 合同生成
- **实时预览**：左侧修改，右侧实时更新
- **专业格式**：标准的商务合同格式
- **自动填充**：根据选择的公司自动应用税率和印章

### 💾 历史记录
- **自动保存**：每次生成的合同自动保存
- **历史管理**：查看、加载、导出、删除历史合同
- **数据恢复**：可以从历史记录中恢复之前的合同数据

## 🔧 系统要求

### 浏览器支持
- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Edge 80+
- ✅ Safari 13+

### 功能依赖
- 需要联网加载Bootstrap样式和图标
- 使用localStorage保存历史记录
- 支持现代JavaScript ES6+语法

## 📁 文件结构

```
合同自动生成器/
├── index.html                    # 主页面文件
├── 产品配置.js                   # 产品数据配置
├── 产品名称模板.json             # JSON格式产品模板
├── 产品清单模板.csv              # CSV格式产品模板
├── 产品导入规则说明.md           # 导入规则说明
├── 产品模板使用说明.md           # 模板使用指南
├── 启动说明.md                   # 本文件
├── 模板.html                     # 合同模板
├── 送货单模板.html               # 送货单模板
├── 浙江登新科技有限公司.png      # 公司印章图片
├── 湖州物物通科技有限公司.png    # 公司印章图片
└── 产品规格书/                   # 产品规格书文件夹
    ├── 1200半自动印刷机/
    ├── 350半自动印刷机/
    ├── ...
    └── 无框印刷台/
```

## ❓ 常见问题

### Q: 页面打开后显示空白或样式错乱？
A: 请确保网络连接正常，系统需要加载在线的Bootstrap样式库。

### Q: 智能识别不准确怎么办？
A: 
1. 确保粘贴的信息格式规范
2. 公司名称必须包含"公司"字样
3. 纳税人识别号应为18位字母数字组合
4. 可以手动修改识别结果

### Q: 产品列表为空？
A: 检查 `产品配置.js` 文件是否存在且格式正确。

### Q: 无法保存历史记录？
A: 请确保浏览器支持localStorage，且没有禁用本地存储。

### Q: 导出PDF功能不工作？
A: 系统会打开打印预览窗口，您可以：
1. 使用浏览器的打印功能
2. 选择"另存为PDF"
3. 调整页面设置后保存

## 🆘 技术支持

如遇到技术问题，请检查：
1. 浏览器控制台是否有错误信息
2. 网络连接是否正常
3. 文件是否完整

---

**开始使用：双击 `index.html` 文件即可开始使用合同自动生成器！**

*最后更新：2025-01-18*
