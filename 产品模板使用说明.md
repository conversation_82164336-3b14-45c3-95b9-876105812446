# 产品名称文件格式模板使用说明

## 概述

本目录包含了合同自动生成系统所需的产品名称文件格式模板，系统可以通过这些模板识别和导入产品信息，自动生成合同。

## 文件清单

### 1. 核心模板文件
- **`产品名称模板.json`** - JSON格式的完整产品信息模板（推荐使用）
- **`产品清单模板.csv`** - CSV格式的简化产品清单模板
- **`产品配置.js`** - JavaScript配置文件，供系统直接调用

### 2. 说明文档
- **`产品导入规则说明.md`** - 详细的导入规则和格式规范
- **`产品模板使用说明.md`** - 本文件，使用指南

## 快速开始

### 方式一：使用JSON模板（推荐）
1. 复制 `产品名称模板.json` 文件
2. 根据实际需要修改产品信息
3. 确保产品名称与 `产品规格书` 文件夹名称完全一致
4. 在系统中导入JSON文件

### 方式二：使用CSV模板
1. 用Excel或文本编辑器打开 `产品清单模板.csv`
2. 修改产品信息，注意保持CSV格式
3. 保存为UTF-8编码的CSV文件
4. 在系统中导入CSV文件

### 方式三：直接使用JS配置
1. 将 `产品配置.js` 集成到系统代码中
2. 系统可直接调用配置信息
3. 支持动态产品管理

## 重要注意事项

### ⚠️ 产品名称匹配规则
- **产品名称必须与产品规格书文件夹名称完全一致**
- 区分大小写
- 不能有多余的空格或特殊字符

### 💰 价格和支付方式
- 单价必须为正数，单位为人民币元
- 系统自动根据总金额选择支付方式：
  - **≥ 100,000元**：分期付款（30% + 60% + 10%）
  - **< 100,000元**：全款付款

### 📋 合同生成规则
- 系统根据选择的拟签约公司自动应用税率：
  - **浙江登新科技有限公司**：13%增值税
  - **湖州物物通科技有限公司**：1%普通电子发票

## 产品信息维护

### 添加新产品
1. 在对应的模板文件中添加产品信息
2. 确保在 `产品规格书` 目录下创建对应的产品文件夹
3. 在产品文件夹中放置 `产品参数表.png` 文件
4. 更新系统配置

### 修改产品价格
1. 在模板文件中更新单价
2. 重新导入到系统中
3. 建议保留价格变更记录

### 删除停产产品
1. 从模板文件中移除产品信息
2. 可选择保留或删除产品规格书文件夹
3. 更新系统配置

## 系统集成

### 导入流程
1. **文件验证** - 检查文件格式和编码
2. **数据验证** - 验证必填字段和数据格式
3. **规格书匹配** - 检查产品规格书文件是否存在
4. **数据导入** - 将产品信息导入系统数据库
5. **生成报告** - 显示导入结果和错误信息

### 错误处理
- **产品名称不匹配**：检查文件夹名称是否正确
- **价格格式错误**：确保价格为正数
- **编码问题**：使用UTF-8编码保存文件
- **必填字段缺失**：补充产品名称和单价

## 最佳实践

### 1. 文件管理
- 定期备份产品数据文件
- 使用版本控制管理模板文件
- 建立产品信息变更审批流程

### 2. 数据质量
- 定期检查产品名称与规格书文件夹的一致性
- 及时更新产品价格
- 保持产品描述信息的准确性

### 3. 系统维护
- 定期清理停产产品信息
- 监控系统导入日志
- 建立数据恢复机制

## 技术支持

### 常见问题
1. **Q: 产品名称包含特殊字符怎么办？**
   A: 确保产品名称与文件夹名称完全一致，包括特殊字符

2. **Q: 如何批量更新产品价格？**
   A: 使用Excel编辑CSV模板，然后重新导入系统

3. **Q: 系统提示找不到产品规格书怎么办？**
   A: 检查产品规格书文件夹是否存在，名称是否与产品名称一致

4. **Q: 如何添加新的产品类别？**
   A: 在JS配置文件的categories部分添加新类别定义

### 联系方式
如有技术问题，请联系系统管理员或查阅系统帮助文档。

---

## 更新日志

### v1.0 (2025-01-18)
- 创建初始版本的产品模板
- 支持JSON、CSV、JS三种格式
- 包含18种产品的完整信息
- 建立标准的导入规则和验证机制

---

*最后更新：2025-01-18*  
*文档版本：1.0*
