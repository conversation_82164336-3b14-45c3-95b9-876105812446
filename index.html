<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合同自动生成器</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background-color: #f5f5f5;
        }
        .contract-preview {
            border: 1px solid #ddd;
            padding: 30px;
            background: white;
            min-height: 800px;
            font-family: 'SimSun', serif;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border-radius: 8px;
        }
        .form-section {
            background: white;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid #0066cc;
        }
        .product-item {
            border: 1px solid #e0e0e0;
            padding: 20px;
            margin-bottom: 15px;
            border-radius: 8px;
            background: #fafafa;
            transition: all 0.3s ease;
        }
        .product-item:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .company-logo {
            width: 45px;
            height: 45px;
            object-fit: contain;
        }
        .navbar-brand {
            font-weight: bold;
            color: #0066cc !important;
            font-size: 1.5rem;
        }
        .btn-primary {
            background: linear-gradient(45deg, #0066cc, #0052a3);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: 500;
        }
        .btn-primary:hover {
            background: linear-gradient(45deg, #0052a3, #003d7a);
            transform: translateY(-1px);
        }
        .alert-info {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border: none;
            border-radius: 10px;
            color: #0277bd;
        }
        .payment-info {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border: none;
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
        }
        .total-amount {
            font-size: 1.4em;
            font-weight: bold;
            color: #d32f2f;
        }
        .section-title {
            color: #0066cc;
            font-weight: 600;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .form-control:focus, .form-select:focus {
            border-color: #0066cc;
            box-shadow: 0 0 0 0.2rem rgba(0, 102, 204, 0.25);
        }
        .navbar {
            background: linear-gradient(135deg, #0066cc, #0052a3) !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .navbar-brand, .navbar-nav .btn {
            color: white !important;
        }
        .contract-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #0066cc;
        }
        .contract-table {
            margin-bottom: 25px;
        }
        .contract-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        .signature-section {
            margin-top: 50px;
            padding-top: 30px;
            border-top: 1px solid #dee2e6;
        }
        .loading-spinner {
            display: none;
        }
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-file-earmark-text me-2"></i>
                合同自动生成器
            </a>
            <div class="navbar-nav ms-auto">
                <button class="btn btn-outline-light me-2" onclick="loadHistory()">
                    <i class="bi bi-clock-history me-1"></i> 历史记录
                </button>
                <button class="btn btn-outline-light me-2" onclick="saveContract()">
                    <i class="bi bi-save me-1"></i> 保存合同
                </button>
                <button class="btn btn-success" onclick="exportPDF()">
                    <i class="bi bi-file-pdf me-1"></i> 导出PDF
                </button>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <!-- 左侧表单区域 -->
            <div class="col-lg-5">
                <!-- 拟签约公司选择 -->
                <div class="form-section fade-in">
                    <h5 class="section-title">
                        <i class="bi bi-building"></i>
                        拟签约公司选择
                    </h5>
                    <div class="mb-3">
                        <select class="form-select" id="contractCompany" onchange="updateCompanyInfo()">
                            <option value="">请选择拟签约公司</option>
                            <option value="浙江登新科技有限公司">浙江登新科技有限公司</option>
                            <option value="湖州物物通科技有限公司">湖州物物通科技有限公司</option>
                        </select>
                    </div>
                    <div id="companyInfo" class="alert alert-info d-none">
                        <div id="companyDetails"></div>
                    </div>
                </div>

                <!-- 客户信息识别 -->
                <div class="form-section fade-in">
                    <h5 class="section-title">
                        <i class="bi bi-person-badge"></i>
                        客户信息智能识别
                    </h5>
                    <div class="mb-3">
                        <label class="form-label fw-bold">客户信息粘贴区</label>
                        <textarea class="form-control" id="customerInfo" rows="6" 
                                placeholder="请粘贴客户发送的完整信息，系统将智能识别：&#10;✓ 公司全称（必须包含'公司'字样）&#10;✓ 纳税人识别号（18位数字+字母组合）&#10;✓ 开户银行（包含'银行'或'行'字样）&#10;✓ 银行账户（10-25位数字）&#10;✓ 公司注册地址&#10;✓ 联系电话（手机号或座机号）"></textarea>
                    </div>
                    <button class="btn btn-primary" onclick="parseCustomerInfo()">
                        <i class="bi bi-cpu me-1"></i> 
                        <span class="loading-spinner spinner-border spinner-border-sm me-1"></span>
                        智能识别客户信息
                    </button>
                    <div id="customerResult" class="mt-3"></div>
                </div>

                <!-- 收件人信息识别 -->
                <div class="form-section fade-in">
                    <h5 class="section-title">
                        <i class="bi bi-truck"></i>
                        收件人信息智能识别
                    </h5>
                    <div class="mb-3">
                        <label class="form-label fw-bold">收件人信息粘贴区</label>
                        <textarea class="form-control" id="recipientInfo" rows="4" 
                                placeholder="请粘贴收件人信息，系统将智能识别：&#10;✓ 收件人姓名（2-4个中文字符）&#10;✓ 收件人手机号（11位数字）&#10;✓ 收件人地址（包含省市区县等地址信息）"></textarea>
                    </div>
                    <button class="btn btn-primary" onclick="parseRecipientInfo()">
                        <i class="bi bi-cpu me-1"></i>
                        <span class="loading-spinner spinner-border spinner-border-sm me-1"></span>
                        智能识别收件信息
                    </button>
                    <div id="recipientResult" class="mt-3"></div>
                </div>

                <!-- 产品选择 -->
                <div class="form-section fade-in">
                    <h5 class="section-title">
                        <i class="bi bi-box-seam"></i>
                        产品选择与配置
                    </h5>
                    <div class="mb-3">
                        <div class="row">
                            <div class="col-8">
                                <select class="form-select" id="productSelect">
                                    <option value="">请选择产品</option>
                                </select>
                            </div>
                            <div class="col-4">
                                <button class="btn btn-primary w-100" onclick="addProduct()">
                                    <i class="bi bi-plus-circle me-1"></i> 添加
                                </button>
                            </div>
                        </div>
                    </div>
                    <div id="selectedProducts"></div>
                    <div class="payment-info">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="fw-bold">合同总金额:</span>
                            <span class="total-amount">¥<span id="totalAmount">0</span></span>
                        </div>
                        <div id="paymentMethod"></div>
                    </div>
                </div>
            </div>

            <!-- 右侧合同预览区域 -->
            <div class="col-lg-7">
                <div class="form-section fade-in">
                    <h5 class="section-title">
                        <i class="bi bi-file-text"></i>
                        合同实时预览
                        <div class="ms-auto">
                            <button class="btn btn-sm btn-outline-primary" onclick="refreshPreview()">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                        </div>
                    </h5>
                    <div class="contract-preview" id="contractPreview">
                        <div class="contract-header">
                            <h2 style="color: #0066cc; margin-bottom: 10px;">设备采购合同</h2>
                            <p class="text-muted mb-0">Contract for Equipment Purchase</p>
                        </div>
                        <div class="text-center text-muted">
                            <i class="bi bi-file-text" style="font-size: 4rem; opacity: 0.3;"></i>
                            <h5 class="mt-3">请完善左侧信息</h5>
                            <p>选择拟签约公司并添加产品后，合同内容将在此实时预览</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 历史记录模态框 -->
    <div class="modal fade" id="historyModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-clock-history me-2"></i>
                        历史记录管理
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="historyList"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-danger" onclick="clearHistory()">清空历史</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="产品配置.js"></script>
    <script>
        // 全局变量
        let selectedProducts = [];
        let customerData = {};
        let recipientData = {};
        let contractCompanyData = {};

        // 公司信息配置
        const companyInfo = {
            "浙江登新科技有限公司": {
                address: "浙江省湖州市安吉县天子湖镇现代工业园区天子湖大道18号",
                taxNumber: "91330523MA2B45AR0R",
                phone: "0571-********",
                bank: "中国农业银行安吉城东支行",
                account: "*****************",
                taxRate: "13%增值税",
                logo: "浙江登新科技有限公司.png"
            },
            "湖州物物通科技有限公司": {
                address: "浙江省湖州市安吉县天子湖镇现代工业园区天子湖大道18号1幢3层301室",
                taxNumber: "91330523MA7B2BJR28",
                phone: "0572-5686506",
                bank: "杭州联合农村商业银行股份有限公司安吉绿色支行天子湖分理处",
                account: "***************",
                taxRate: "1%普通电子发票",
                logo: "湖州物物通科技有限公司.png"
            }
        };

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            loadProducts();
            updateContractDate();
            // 添加淡入动画
            setTimeout(() => {
                document.querySelectorAll('.fade-in').forEach((el, index) => {
                    setTimeout(() => {
                        el.style.opacity = '1';
                        el.style.transform = 'translateY(0)';
                    }, index * 100);
                });
            }, 100);
        });

        // 加载产品列表
        function loadProducts() {
            const productSelect = document.getElementById('productSelect');
            const products = ProductUtils.getAllProductNames();

            // 按类别分组显示产品
            const categories = {};
            products.forEach(productName => {
                const product = ProductUtils.getProduct(productName);
                if (!categories[product.category]) {
                    categories[product.category] = [];
                }
                categories[product.category].push({name: productName, product});
            });

            // 创建分组选项
            Object.keys(categories).forEach(category => {
                const optgroup = document.createElement('optgroup');
                optgroup.label = category;

                categories[category].forEach(({name, product}) => {
                    const option = document.createElement('option');
                    option.value = name;
                    option.textContent = `${name} - ¥${product.price.toLocaleString()}`;
                    optgroup.appendChild(option);
                });

                productSelect.appendChild(optgroup);
            });
        }

        // 更新拟签约公司信息
        function updateCompanyInfo() {
            const companySelect = document.getElementById('contractCompany');
            const companyInfoDiv = document.getElementById('companyInfo');
            const companyDetailsDiv = document.getElementById('companyDetails');

            const selectedCompany = companySelect.value;
            if (selectedCompany && companyInfo[selectedCompany]) {
                contractCompanyData = companyInfo[selectedCompany];
                contractCompanyData.name = selectedCompany;

                companyDetailsDiv.innerHTML = `
                    <div class="row">
                        <div class="col-md-6">
                            <strong><i class="bi bi-building me-1"></i>${selectedCompany}</strong><br>
                            <small class="text-muted">
                                <i class="bi bi-geo-alt me-1"></i>地址：${contractCompanyData.address}<br>
                                <i class="bi bi-credit-card me-1"></i>税号：${contractCompanyData.taxNumber}<br>
                                <i class="bi bi-telephone me-1"></i>电话：${contractCompanyData.phone}
                            </small>
                        </div>
                        <div class="col-md-6">
                            <small class="text-muted">
                                <i class="bi bi-bank me-1"></i>开户行：${contractCompanyData.bank}<br>
                                <i class="bi bi-credit-card-2-front me-1"></i>账号：${contractCompanyData.account}<br>
                                <i class="bi bi-percent me-1"></i>税率：${contractCompanyData.taxRate}
                            </small>
                        </div>
                    </div>
                `;
                companyInfoDiv.classList.remove('d-none');
            } else {
                companyInfoDiv.classList.add('d-none');
                contractCompanyData = {};
            }
            updateContractPreview();
        }

        // 显示加载动画
        function showLoading(buttonElement) {
            const spinner = buttonElement.querySelector('.loading-spinner');
            const icon = buttonElement.querySelector('.bi:not(.loading-spinner .bi)');
            if (spinner) spinner.style.display = 'inline-block';
            if (icon) icon.style.display = 'none';
            buttonElement.disabled = true;
        }

        // 隐藏加载动画
        function hideLoading(buttonElement) {
            const spinner = buttonElement.querySelector('.loading-spinner');
            const icon = buttonElement.querySelector('.bi:not(.loading-spinner .bi)');
            if (spinner) spinner.style.display = 'none';
            if (icon) icon.style.display = 'inline-block';
            buttonElement.disabled = false;
        }

        // 解析客户信息
        function parseCustomerInfo() {
            const button = event.target;
            const text = document.getElementById('customerInfo').value;
            const resultDiv = document.getElementById('customerResult');

            if (!text.trim()) {
                showAlert('请先输入客户信息', 'warning');
                return;
            }

            showLoading(button);

            // 模拟处理时间
            setTimeout(() => {
                // 增强的正则表达式匹配
                const patterns = {
                    company: /([^\r\n，,。.\s]*(?:有限责任公司|股份有限公司|有限公司|集团公司|公司)[^\r\n，,。.\s]*)/,
                    taxNumber: /([A-Z0-9]{15,20})/,
                    bank: /([^\r\n，,。.\s]*(?:银行|农商行|信用社|邮储)[^\r\n，,。.\s]*)/,
                    account: /(\d{10,25})/,
                    phone: /(\d{3,4}[-\s]?\d{7,8}|\d{11})/,
                    address: /([^\r\n，,。.]*(?:省|市|区|县|镇|街道|路|号|室|层|楼)[^\r\n，,。.]*)/
                };

                customerData = {};
                for (const [key, pattern] of Object.entries(patterns)) {
                    const match = text.match(pattern);
                    if (match) {
                        customerData[key] = match[1].trim();
                    }
                }

                // 显示识别结果
                const fields = [
                    {key: 'company', label: '公司全称', icon: 'building'},
                    {key: 'taxNumber', label: '纳税人识别号', icon: 'credit-card'},
                    {key: 'bank', label: '开户银行', icon: 'bank'},
                    {key: 'account', label: '银行账户', icon: 'credit-card-2-front'},
                    {key: 'phone', label: '联系电话', icon: 'telephone'},
                    {key: 'address', label: '注册地址', icon: 'geo-alt'}
                ];

                let resultHtml = '<div class="alert alert-success"><h6><i class="bi bi-check-circle me-1"></i>识别结果：</h6>';
                fields.forEach(field => {
                    const value = customerData[field.key];
                    const status = value ? 'success' : 'danger';
                    const statusIcon = value ? 'check-circle' : 'x-circle';
                    resultHtml += `
                        <div class="d-flex align-items-center mb-1">
                            <i class="bi bi-${field.icon} me-2 text-primary"></i>
                            <span class="me-2">${field.label}:</span>
                            <span class="text-${status}">
                                <i class="bi bi-${statusIcon} me-1"></i>
                                ${value || '未识别'}
                            </span>
                        </div>
                    `;
                });
                resultHtml += '</div>';

                resultDiv.innerHTML = resultHtml;
                hideLoading(button);
                updateContractPreview();

                if (Object.keys(customerData).length > 0) {
                    showAlert('客户信息识别完成！', 'success');
                }
            }, 1000);
        }

        // 解析收件人信息
        function parseRecipientInfo() {
            const button = event.target;
            const text = document.getElementById('recipientInfo').value;
            const resultDiv = document.getElementById('recipientResult');

            if (!text.trim()) {
                showAlert('请先输入收件人信息', 'warning');
                return;
            }

            showLoading(button);

            setTimeout(() => {
                const patterns = {
                    name: /([^\d\s\r\n，,。.]{2,4})/,
                    phone: /(\d{11})/,
                    address: /([^\r\n，,。.]*(?:省|市|区|县|镇|街道|路|号|室|层|楼)[^\r\n，,。.]*)/
                };

                recipientData = {};
                for (const [key, pattern] of Object.entries(patterns)) {
                    const match = text.match(pattern);
                    if (match) {
                        recipientData[key] = match[1].trim();
                    }
                }

                const fields = [
                    {key: 'name', label: '收件人姓名', icon: 'person'},
                    {key: 'phone', label: '收件人手机', icon: 'phone'},
                    {key: 'address', label: '收件人地址', icon: 'geo-alt-fill'}
                ];

                let resultHtml = '<div class="alert alert-success"><h6><i class="bi bi-check-circle me-1"></i>识别结果：</h6>';
                fields.forEach(field => {
                    const value = recipientData[field.key];
                    const status = value ? 'success' : 'danger';
                    const statusIcon = value ? 'check-circle' : 'x-circle';
                    resultHtml += `
                        <div class="d-flex align-items-center mb-1">
                            <i class="bi bi-${field.icon} me-2 text-primary"></i>
                            <span class="me-2">${field.label}:</span>
                            <span class="text-${status}">
                                <i class="bi bi-${statusIcon} me-1"></i>
                                ${value || '未识别'}
                            </span>
                        </div>
                    `;
                });
                resultHtml += '</div>';

                resultDiv.innerHTML = resultHtml;
                hideLoading(button);
                updateContractPreview();

                if (Object.keys(recipientData).length > 0) {
                    showAlert('收件人信息识别完成！', 'success');
                }
            }, 800);
        }

        // 添加产品
        function addProduct() {
            const productSelect = document.getElementById('productSelect');
            const productName = productSelect.value;

            if (!productName) {
                showAlert('请先选择产品', 'warning');
                return;
            }

            const product = ProductUtils.getProduct(productName);
            if (!product) {
                showAlert('产品信息获取失败', 'error');
                return;
            }

            // 检查是否已添加
            const existingIndex = selectedProducts.findIndex(p => p.name === productName);
            if (existingIndex >= 0) {
                selectedProducts[existingIndex].quantity += 1;
                showAlert(`${productName} 数量已增加`, 'info');
            } else {
                selectedProducts.push({
                    name: productName,
                    model: product.model,
                    price: product.price,
                    quantity: 1,
                    category: product.category,
                    description: product.description
                });
                showAlert(`已添加 ${productName}`, 'success');
            }

            productSelect.value = '';
            updateProductList();
            updateContractPreview();
        }

        // 更新产品列表显示
        function updateProductList() {
            const container = document.getElementById('selectedProducts');
            container.innerHTML = '';

            if (selectedProducts.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-box" style="font-size: 2rem;"></i>
                        <p class="mt-2">暂未选择产品</p>
                    </div>
                `;
                document.getElementById('totalAmount').textContent = '0';
                document.getElementById('paymentMethod').innerHTML = '';
                return;
            }

            let total = 0;
            selectedProducts.forEach((product, index) => {
                const subtotal = product.price * product.quantity;
                total += subtotal;

                const productDiv = document.createElement('div');
                productDiv.className = 'product-item';
                productDiv.innerHTML = `
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h6 class="mb-1 text-primary">${product.name}</h6>
                            <small class="text-muted d-block">${product.model}</small>
                            <small class="text-muted">${product.category}</small>
                            <div class="mt-1">
                                <span class="badge bg-secondary">单价: ¥${product.price.toLocaleString()}</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label small">数量</label>
                            <div class="input-group input-group-sm">
                                <button class="btn btn-outline-secondary" onclick="changeQuantity(${index}, -1)">
                                    <i class="bi bi-dash"></i>
                                </button>
                                <input type="number" class="form-control text-center" value="${product.quantity}"
                                       onchange="setQuantity(${index}, this.value)" min="1">
                                <button class="btn btn-outline-secondary" onclick="changeQuantity(${index}, 1)">
                                    <i class="bi bi-plus"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-3 text-end">
                            <div class="fw-bold text-success mb-2">¥${subtotal.toLocaleString()}</div>
                            <button class="btn btn-sm btn-outline-danger" onclick="removeProduct(${index})"
                                    title="删除产品">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                `;
                container.appendChild(productDiv);
            });

            // 更新总金额和支付方式
            document.getElementById('totalAmount').textContent = total.toLocaleString();

            const paymentMethod = ProductUtils.getPaymentMethod(total);
            const paymentDiv = document.getElementById('paymentMethod');

            if (paymentMethod === 'installment') {
                const installment = ProductUtils.calculateInstallment(total);
                paymentDiv.innerHTML = `
                    <div class="mt-3">
                        <div class="d-flex align-items-center mb-2">
                            <i class="bi bi-credit-card me-2 text-warning"></i>
                            <strong class="text-warning">分期付款方式</strong>
                        </div>
                        <div class="row text-sm">
                            <div class="col-4 text-center">
                                <div class="border rounded p-2">
                                    <div class="fw-bold text-primary">预付定金</div>
                                    <div class="text-success">¥${installment.deposit.toLocaleString()}</div>
                                    <small class="text-muted">30%</small>
                                </div>
                            </div>
                            <div class="col-4 text-center">
                                <div class="border rounded p-2">
                                    <div class="fw-bold text-primary">发货款</div>
                                    <div class="text-success">¥${installment.shipping.toLocaleString()}</div>
                                    <small class="text-muted">60%</small>
                                </div>
                            </div>
                            <div class="col-4 text-center">
                                <div class="border rounded p-2">
                                    <div class="fw-bold text-primary">收货尾款</div>
                                    <div class="text-success">¥${installment.final.toLocaleString()}</div>
                                    <small class="text-muted">10%</small>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            } else {
                paymentDiv.innerHTML = `
                    <div class="mt-3">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-cash-coin me-2 text-success"></i>
                            <strong class="text-success">全款付款方式</strong>
                        </div>
                        <small class="text-muted">全款到账，款到发货</small>
                    </div>
                `;
            }
        }

        // 修改产品数量
        function changeQuantity(index, delta) {
            selectedProducts[index].quantity = Math.max(1, selectedProducts[index].quantity + delta);
            updateProductList();
            updateContractPreview();
        }

        // 设置产品数量
        function setQuantity(index, value) {
            const quantity = Math.max(1, parseInt(value) || 1);
            selectedProducts[index].quantity = quantity;
            updateProductList();
            updateContractPreview();
        }

        // 删除产品
        function removeProduct(index) {
            const productName = selectedProducts[index].name;
            if (confirm(`确定要删除 ${productName} 吗？`)) {
                selectedProducts.splice(index, 1);
                updateProductList();
                updateContractPreview();
                showAlert(`已删除 ${productName}`, 'info');
            }
        }

        // 更新合同日期
        function updateContractDate() {
            const today = new Date();
            const year = today.getFullYear();
            const month = String(today.getMonth() + 1).padStart(2, '0');
            const day = String(today.getDate()).padStart(2, '0');
            return `${year}年${month}月${day}日`;
        }

        // 数字转中文大写
        function numberToChinese(num) {
            const digits = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
            const units = ['', '拾', '佰', '仟', '万', '拾万', '佰万', '仟万', '亿'];

            if (num === 0) return '零元';

            let result = '';
            let numStr = num.toString();
            let len = numStr.length;

            for (let i = 0; i < len; i++) {
                let digit = parseInt(numStr[i]);
                let unit = units[len - i - 1];

                if (digit !== 0) {
                    result += digits[digit] + unit;
                } else if (result && !result.endsWith('零')) {
                    result += '零';
                }
            }

            return result.replace(/零+$/, '') + '元';
        }

        // 显示提示消息
        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';

            const icons = {
                success: 'check-circle',
                error: 'x-circle',
                warning: 'exclamation-triangle',
                info: 'info-circle'
            };

            alertDiv.innerHTML = `
                <i class="bi bi-${icons[type]} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alertDiv);

            // 自动消失
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }

        // 刷新预览
        function refreshPreview() {
            updateContractPreview();
            showAlert('合同预览已刷新', 'success');
        }

        // 更新合同预览
        function updateContractPreview() {
            const preview = document.getElementById('contractPreview');

            if (!contractCompanyData.name || selectedProducts.length === 0) {
                preview.innerHTML = `
                    <div class="contract-header">
                        <h2 style="color: #0066cc; margin-bottom: 10px;">设备采购合同</h2>
                        <p class="text-muted mb-0">Contract for Equipment Purchase</p>
                    </div>
                    <div class="text-center text-muted">
                        <i class="bi bi-file-text" style="font-size: 4rem; opacity: 0.3;"></i>
                        <h5 class="mt-3">请完善左侧信息</h5>
                        <p>选择拟签约公司并添加产品后，合同内容将在此实时预览</p>
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <div class="card border-0 bg-light">
                                    <div class="card-body text-center">
                                        <i class="bi bi-building text-primary" style="font-size: 2rem;"></i>
                                        <h6 class="mt-2">选择拟签约公司</h6>
                                        <small class="text-muted">浙江登新科技 或 湖州物物通科技</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-0 bg-light">
                                    <div class="card-body text-center">
                                        <i class="bi bi-box-seam text-primary" style="font-size: 2rem;"></i>
                                        <h6 class="mt-2">添加产品</h6>
                                        <small class="text-muted">从产品列表中选择设备</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                return;
            }

            const total = selectedProducts.reduce((sum, p) => sum + p.price * p.quantity, 0);
            const contractDate = updateContractDate();
            const paymentMethod = ProductUtils.getPaymentMethod(total);

            let paymentContent = '';
            if (paymentMethod === 'installment') {
                const installment = ProductUtils.calculateInstallment(total);
                paymentContent = `
                    <p><strong>分期付款方式：</strong></p>
                    <p>预付定金为：<span class="text-primary fw-bold">${installment.deposit.toLocaleString()}元</span>，大写：<span class="text-primary fw-bold">${numberToChinese(installment.deposit)}</span>圆整；收到货款后，设备开始进入生产；</p>
                    <p>发货款为：<span class="text-primary fw-bold">${installment.shipping.toLocaleString()}元</span>，大写：<span class="text-primary fw-bold">${numberToChinese(installment.shipping)}</span>圆整；收到货款后，设备进入发货流程；</p>
                    <p>收货尾款为：<span class="text-primary fw-bold">${installment.final.toLocaleString()}元</span>，大写：<span class="text-primary fw-bold">${numberToChinese(installment.final)}</span>圆整；设备到货后15日内，支付尾款。</p>
                `;
            } else {
                paymentContent = '<p><strong>付款方式：</strong>全款到账，款到发货</p>';
            }

            let productTable = '';
            selectedProducts.forEach((product, index) => {
                const subtotal = product.price * product.quantity;
                productTable += `
                    <tr>
                        <td class="text-center">${index + 1}</td>
                        <td>${product.name}</td>
                        <td class="text-center">${product.model}</td>
                        <td class="text-center">${product.quantity}</td>
                        <td class="text-center">台</td>
                        <td class="text-end">¥${product.price.toLocaleString()}</td>
                        <td class="text-end fw-bold">¥${subtotal.toLocaleString()}</td>
                    </tr>
                `;
            });

            preview.innerHTML = `
                <div class="contract-header">
                    <h2 style="color: #0066cc; margin-bottom: 10px;">设备采购合同</h2>
                    <p class="text-muted mb-2">Contract for Equipment Purchase</p>
                    <p class="mb-0"><strong>合同编号：</strong>NEODEN-${contractDate.replace(/[年月日]/g, '')}</p>
                </div>

                <table class="table table-bordered contract-table">
                    <tr>
                        <td width="15%" class="fw-bold bg-light">甲方（需方）</td>
                        <td width="35%">${customerData.company || '<span class="text-muted">[客户公司名称]</span>'}</td>
                        <td width="15%" class="fw-bold bg-light">乙方（供方）</td>
                        <td width="35%">${contractCompanyData.name}</td>
                    </tr>
                    <tr>
                        <td class="fw-bold bg-light">收件人姓名</td>
                        <td>${recipientData.name || '<span class="text-muted">[收件人姓名]</span>'}</td>
                        <td class="fw-bold bg-light">拟签约时间</td>
                        <td>${contractDate}</td>
                    </tr>
                    <tr>
                        <td class="fw-bold bg-light">收件人手机号</td>
                        <td>${recipientData.phone || '<span class="text-muted">[收件人手机号]</span>'}</td>
                        <td class="fw-bold bg-light">合同编号</td>
                        <td>NEODEN-${contractDate.replace(/[年月日]/g, '')}</td>
                    </tr>
                    <tr>
                        <td class="fw-bold bg-light">收件人地址</td>
                        <td colspan="3">${recipientData.address || '<span class="text-muted">[收件人地址]</span>'}</td>
                    </tr>
                </table>

                <h5 class="text-primary mb-3"><i class="bi bi-list-ol me-2"></i>一、标的物</h5>
                <table class="table table-bordered contract-table">
                    <thead class="table-light">
                        <tr>
                            <th width="8%" class="text-center">序号</th>
                            <th width="25%">产品名称</th>
                            <th width="15%" class="text-center">型号</th>
                            <th width="8%" class="text-center">数量</th>
                            <th width="8%" class="text-center">单位</th>
                            <th width="15%" class="text-center">单价</th>
                            <th width="15%" class="text-center">金额</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${productTable}
                        <tr class="table-warning">
                            <td colspan="6" class="text-end fw-bold">合同总金额</td>
                            <td class="text-end fw-bold text-danger" style="font-size: 1.1em;">¥${total.toLocaleString()}</td>
                        </tr>
                    </tbody>
                </table>

                <div class="mb-4">
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" ${contractCompanyData.taxRate?.includes('13%') ? 'checked' : ''} disabled>
                        <label class="form-check-label">以上价格含 13%增值税</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" ${contractCompanyData.taxRate?.includes('1%') ? 'checked' : ''} disabled>
                        <label class="form-check-label">以上价格含 1%普通电子发票</label>
                    </div>
                </div>

                <h5 class="text-primary mb-3"><i class="bi bi-credit-card me-2"></i>二、付款方式</h5>
                <div class="mb-4 p-3 bg-light rounded">
                    <p class="mb-2"><strong>总货款：</strong><span class="text-danger fw-bold">${total.toLocaleString()}元</span>，大写：<span class="text-danger fw-bold">${numberToChinese(total)}</span>圆整</p>
                    ${paymentContent}
                </div>

                <div class="signature-section">
                    <div class="row">
                        <div class="col-6">
                            <div class="border rounded p-3">
                                <h6 class="text-primary mb-3"><i class="bi bi-person-badge me-2"></i>甲方（需方）</h6>
                                <p><strong>公司：</strong>${customerData.company || '<span class="text-muted">[客户公司名称]</span>'}</p>
                                <p><strong>授权代表人：</strong>_______________</p>
                                <p><strong>日期：</strong>${contractDate}</p>
                                <div class="text-center mt-3">
                                    <div class="border rounded p-2 bg-light" style="height: 60px; line-height: 60px;">
                                        <small class="text-muted">甲方签章</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="border rounded p-3">
                                <h6 class="text-primary mb-3"><i class="bi bi-building me-2"></i>乙方（供方）</h6>
                                <p><strong>公司：</strong>${contractCompanyData.name}</p>
                                <p><strong>授权代表人：</strong>_______________</p>
                                <p><strong>日期：</strong>${contractDate}</p>
                                <div class="text-center mt-3">
                                    ${contractCompanyData.logo ?
                                        `<img src="${contractCompanyData.logo}" class="company-logo border rounded" alt="公司印章" style="width: 60px; height: 60px;">` :
                                        `<div class="border rounded p-2 bg-light" style="height: 60px; line-height: 60px;"><small class="text-muted">乙方签章</small></div>`
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // 保存合同
        function saveContract() {
            if (selectedProducts.length === 0) {
                showAlert('请先添加产品', 'warning');
                return;
            }

            if (!customerData.company) {
                showAlert('请先识别客户信息', 'warning');
                return;
            }

            const contractData = {
                id: Date.now(),
                date: new Date().toISOString(),
                customerCompany: customerData.company,
                contractCompany: contractCompanyData.name,
                products: selectedProducts,
                customerData: customerData,
                recipientData: recipientData,
                total: selectedProducts.reduce((sum, p) => sum + p.price * p.quantity, 0)
            };

            // 保存到localStorage
            let history = JSON.parse(localStorage.getItem('contractHistory') || '[]');
            history.unshift(contractData);

            // 只保留最近50条记录
            if (history.length > 50) {
                history = history.slice(0, 50);
            }

            localStorage.setItem('contractHistory', JSON.stringify(history));
            showAlert('合同已保存到历史记录', 'success');
        }

        // 导出PDF功能
        function exportPDF() {
            if (selectedProducts.length === 0) {
                showAlert('请先添加产品', 'warning');
                return;
            }

            // 保存合同到历史记录
            saveContract();

            // 打印预览
            const printWindow = window.open('', '_blank');
            const contractContent = document.getElementById('contractPreview').innerHTML;

            printWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>设备采购合同</title>
                    <style>
                        body { font-family: 'SimSun', serif; margin: 20px; line-height: 1.6; }
                        .contract-header { text-align: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #0066cc; }
                        .table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                        .table th, .table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                        .table th { background-color: #f8f9fa; font-weight: bold; }
                        .text-center { text-align: center; }
                        .text-end { text-align: right; }
                        .fw-bold { font-weight: bold; }
                        .text-primary { color: #0066cc; }
                        .text-danger { color: #dc3545; }
                        .text-muted { color: #6c757d; }
                        .bg-light { background-color: #f8f9fa; }
                        .table-warning { background-color: #fff3cd; }
                        .border { border: 1px solid #ddd; }
                        .rounded { border-radius: 5px; }
                        .p-3 { padding: 15px; }
                        .mb-2 { margin-bottom: 10px; }
                        .mb-3 { margin-bottom: 15px; }
                        .mb-4 { margin-bottom: 20px; }
                        .mt-3 { margin-top: 15px; }
                        .row { display: flex; }
                        .col-6 { width: 50%; padding: 0 10px; }
                        .signature-section { margin-top: 50px; padding-top: 30px; border-top: 1px solid #dee2e6; }
                        @media print {
                            body { margin: 0; }
                            .no-print { display: none; }
                        }
                    </style>
                </head>
                <body>
                    ${contractContent}
                    <div class="no-print" style="text-align: center; margin-top: 30px;">
                        <button onclick="window.print()" style="padding: 10px 20px; background: #0066cc; color: white; border: none; border-radius: 5px; cursor: pointer;">打印合同</button>
                        <button onclick="window.close()" style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer; margin-left: 10px;">关闭</button>
                    </div>
                </body>
                </html>
            `);

            printWindow.document.close();
            showAlert('合同预览窗口已打开，可进行打印或保存为PDF', 'success');
        }

        // 加载历史记录
        function loadHistory() {
            const history = JSON.parse(localStorage.getItem('contractHistory') || '[]');
            const historyList = document.getElementById('historyList');

            if (history.length === 0) {
                historyList.innerHTML = `
                    <div class="text-center text-muted py-5">
                        <i class="bi bi-inbox" style="font-size: 4rem; opacity: 0.3;"></i>
                        <h5 class="mt-3">暂无历史记录</h5>
                        <p>生成的合同将自动保存在这里</p>
                    </div>
                `;
            } else {
                let historyHtml = '<div class="row">';
                history.forEach((contract, index) => {
                    const date = new Date(contract.date).toLocaleString('zh-CN');
                    historyHtml += `
                        <div class="col-md-6 mb-3">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">${contract.customerCompany}</h6>
                                    <small class="text-muted">${date}</small>
                                </div>
                                <div class="card-body">
                                    <p class="mb-1"><strong>签约公司:</strong> ${contract.contractCompany}</p>
                                    <p class="mb-1"><strong>产品数量:</strong> ${contract.products.length} 种</p>
                                    <p class="mb-3"><strong>合同金额:</strong> <span class="text-danger fw-bold">¥${contract.total.toLocaleString()}</span></p>
                                    <div class="btn-group w-100">
                                        <button class="btn btn-sm btn-primary" onclick="loadContractData(${index})">
                                            <i class="bi bi-arrow-clockwise me-1"></i>加载
                                        </button>
                                        <button class="btn btn-sm btn-success" onclick="exportHistoryContract(${index})">
                                            <i class="bi bi-download me-1"></i>导出
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" onclick="deleteHistoryContract(${index})">
                                            <i class="bi bi-trash me-1"></i>删除
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                });
                historyHtml += '</div>';
                historyList.innerHTML = historyHtml;
            }

            const historyModal = new bootstrap.Modal(document.getElementById('historyModal'));
            historyModal.show();
        }

        // 加载历史合同数据
        function loadContractData(index) {
            const history = JSON.parse(localStorage.getItem('contractHistory') || '[]');
            const contract = history[index];

            if (!contract) return;

            // 恢复数据
            customerData = contract.customerData || {};
            recipientData = contract.recipientData || {};
            selectedProducts = contract.products || [];

            // 更新界面
            document.getElementById('contractCompany').value = contract.contractCompany || '';
            updateCompanyInfo();

            // 显示识别结果
            if (Object.keys(customerData).length > 0) {
                const resultDiv = document.getElementById('customerResult');
                resultDiv.innerHTML = '<div class="alert alert-info">已从历史记录加载客户信息</div>';
            }

            if (Object.keys(recipientData).length > 0) {
                const resultDiv = document.getElementById('recipientResult');
                resultDiv.innerHTML = '<div class="alert alert-info">已从历史记录加载收件人信息</div>';
            }

            updateProductList();
            updateContractPreview();

            // 关闭模态框
            const historyModal = bootstrap.Modal.getInstance(document.getElementById('historyModal'));
            historyModal.hide();

            showAlert('历史合同数据已加载', 'success');
        }

        // 导出历史合同
        function exportHistoryContract(index) {
            const history = JSON.parse(localStorage.getItem('contractHistory') || '[]');
            const contract = history[index];

            if (!contract) return;

            // 临时加载数据并导出
            const tempCustomerData = customerData;
            const tempRecipientData = recipientData;
            const tempSelectedProducts = selectedProducts;
            const tempContractCompanyData = contractCompanyData;

            customerData = contract.customerData || {};
            recipientData = contract.recipientData || {};
            selectedProducts = contract.products || [];
            contractCompanyData = companyInfo[contract.contractCompany] || {};
            contractCompanyData.name = contract.contractCompany;

            updateContractPreview();
            exportPDF();

            // 恢复原数据
            customerData = tempCustomerData;
            recipientData = tempRecipientData;
            selectedProducts = tempSelectedProducts;
            contractCompanyData = tempContractCompanyData;

            updateContractPreview();
        }

        // 删除历史合同
        function deleteHistoryContract(index) {
            if (!confirm('确定要删除这条历史记录吗？')) return;

            let history = JSON.parse(localStorage.getItem('contractHistory') || '[]');
            history.splice(index, 1);
            localStorage.setItem('contractHistory', JSON.stringify(history));

            loadHistory(); // 重新加载历史记录
            showAlert('历史记录已删除', 'info');
        }

        // 清空历史记录
        function clearHistory() {
            if (!confirm('确定要清空所有历史记录吗？此操作不可恢复！')) return;

            localStorage.removeItem('contractHistory');
            loadHistory(); // 重新加载历史记录
            showAlert('历史记录已清空', 'info');
        }
    </script>
</body>
</html>
