<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合同自动生成器</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <h1>合同自动生成器</h1>
        
        <div class="form-section">
            <h2>甲方信息（需方）</h2>
            <div class="input-group">
                <label>客户信息（粘贴后自动识别）:</label>
                <textarea id="customerInfo" placeholder="请粘贴客户提供的公司信息..."></textarea>
                <button onclick="parseCustomerInfo()">自动识别</button>
            </div>
            
            <div class="parsed-info">
                <div class="info-item">
                    <label>公司全称：</label>
                    <input type="text" id="companyName" placeholder="公司全称">
                </div>
                <div class="info-item">
                    <label>纳税人识别号：</label>
                    <input type="text" id="taxId" placeholder="纳税人识别号">
                </div>
                <div class="info-item">
                    <label>开户银行：</label>
                    <input type="text" id="bank" placeholder="开户银行">
                </div>
                <div class="info-item">
                    <label>银行账户：</label>
                    <input type="text" id="bankAccount" placeholder="银行账户">
                </div>
                <div class="info-item">
                    <label>公司注册地址：</label>
                    <input type="text" id="address" placeholder="公司注册地址">
                </div>
                <div class="info-item">
                    <label>联系电话：</label>
                    <input type="text" id="phone" placeholder="联系电话">
                </div>
            </div>
        </div>

        <div class="form-section">
            <h2>收件人信息</h2>
            <div class="input-group">
                <label>收件人信息（粘贴后自动识别）:</label>
                <textarea id="recipientInfo" placeholder="请粘贴收件人信息..."></textarea>
                <button onclick="parseRecipientInfo()">自动识别</button>
            </div>
            
            <div class="parsed-info">
                <div class="info-item">
                    <label>收件人姓名：</label>
                    <input type="text" id="recipientName" placeholder="收件人姓名">
                </div>
                <div class="info-item">
                    <label>收件人手机号：</label>
                    <input type="text" id="recipientPhone" placeholder="收件人手机号">
                </div>
                <div class="info-item">
                    <label>收件人地址：</label>
                    <input type="text" id="recipientAddress" placeholder="收件人地址">
                </div>
            </div>
        </div>

        <div class="form-section">
            <h2>乙方信息（供方）</h2>
            <select id="supplierCompany" onchange="updateSupplierInfo()">
                <option value="">请选择拟签约公司</option>
                <option value="浙江登新科技有限公司">浙江登新科技有限公司</option>
                <option value="湖州物物通科技有限公司">湖州物物通科技有限公司</option>
            </select>
        </div>

        <div class="form-section">
            <h2>产品信息</h2>
            <div id="productList">
                <div class="product-item">
                    <select class="product-select" onchange="updateProductPrice(this)">
                        <option value="">选择产品</option>
                    </select>
                    <input type="number" class="product-quantity" placeholder="数量" value="1" onchange="calculateTotal()">
                    <span class="product-price">0</span>
                    <button onclick="removeProduct(this)">删除</button>
                </div>
            </div>
            <button onclick="addProduct()">添加产品</button>
            <div class="total-amount">
                <strong>总金额: ¥<span id="totalAmount">0</span></strong>
            </div>
        </div>

        <div class="form-section">
            <h2>操作</h2>
            <div class="action-buttons">
                <button onclick="generateContract()" class="generate-btn">生成合同</button>
                <button onclick="exportPDF()" class="export-btn">导出PDF</button>
                <button onclick="showHistory()" class="history-btn">历史记录</button>
            </div>
        </div>
    </div>

    <div id="contractPreview" class="preview-section">
        <h2>合同预览</h2>
        <iframe id="previewFrame" src="模板.htm" onload="initPreview()"></iframe>
    </div>

    <div id="historyModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeHistory()">&times;</span>
            <h2>历史记录</h2>
            <div id="historyList"></div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>


