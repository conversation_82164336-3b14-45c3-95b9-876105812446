<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合同自动生成器</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background-color: #f5f5f5;
        }
        .contract-preview {
            border: 1px solid #ddd;
            padding: 36pt;
            background: white;
            width: 595.3pt;
            min-height: 841.9pt;
            font-family: '微软雅黑', 'SimSun', serif;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border-radius: 8px;
            margin: 0 auto;
            font-size: 10.5pt;
            line-height: 24pt;
            page-break-inside: avoid;
        }

        @media print {
            .contract-preview {
                width: 210mm;
                min-height: 297mm;
                padding: 20mm;
                margin: 0;
                box-shadow: none;
                border: none;
                border-radius: 0;
            }
        }
        .form-section {
            background: white;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid #0066cc;
        }
        .product-item {
            border: 1px solid #e0e0e0;
            padding: 20px;
            margin-bottom: 15px;
            border-radius: 8px;
            background: #fafafa;
            transition: all 0.3s ease;
        }
        .product-item:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .company-logo {
            width: 80px;
            height: 80px;
            object-fit: contain;
            position: absolute;
            z-index: 10;
            opacity: 0.8;
        }

        .signature-with-seal {
            position: relative;
            display: inline-block;
        }

        .contract-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15pt;
            font-size: 9pt;
        }

        .contract-table td, .contract-table th {
            border: 0.5pt solid #000;
            padding: 5.4pt;
            vertical-align: top;
        }

        .contract-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            text-align: center;
        }

        .dashed-underline {
            border-bottom: 1pt dashed #000;
            display: inline-block;
            min-width: 100pt;
        }
        .navbar-brand {
            font-weight: bold;
            color: #0066cc !important;
            font-size: 1.5rem;
        }
        .btn-primary {
            background: linear-gradient(45deg, #0066cc, #0052a3);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: 500;
        }
        .btn-primary:hover {
            background: linear-gradient(45deg, #0052a3, #003d7a);
            transform: translateY(-1px);
        }
        .alert-info {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border: none;
            border-radius: 10px;
            color: #0277bd;
        }
        .payment-info {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border: none;
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
        }
        .total-amount {
            font-size: 1.4em;
            font-weight: bold;
            color: #d32f2f;
        }
        .section-title {
            color: #0066cc;
            font-weight: 600;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .form-control:focus, .form-select:focus {
            border-color: #0066cc;
            box-shadow: 0 0 0 0.2rem rgba(0, 102, 204, 0.25);
        }
        .navbar {
            background: linear-gradient(135deg, #0066cc, #0052a3) !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .navbar-brand, .navbar-nav .btn {
            color: white !important;
        }
        .contract-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #0066cc;
        }
        .contract-table {
            margin-bottom: 25px;
        }
        .contract-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        .signature-section {
            margin-top: 50px;
            padding-top: 30px;
            border-top: 1px solid #dee2e6;
        }
        .loading-spinner {
            display: none;
        }
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-file-earmark-text me-2"></i>
                合同自动生成器
            </a>
            <div class="navbar-nav ms-auto">
                <button class="btn btn-outline-light me-2" onclick="loadHistory()">
                    <i class="bi bi-clock-history me-1"></i> 历史记录
                </button>
                <button class="btn btn-outline-light me-2" onclick="saveContract()">
                    <i class="bi bi-save me-1"></i> 保存合同
                </button>
                <button class="btn btn-outline-light me-2" onclick="generateDeliveryNote()">
                    <i class="bi bi-truck me-1"></i> 生成送货单
                </button>
                <button class="btn btn-success" onclick="exportPDF()">
                    <i class="bi bi-file-pdf me-1"></i> 导出PDF
                </button>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <!-- 左侧表单区域 -->
            <div class="col-lg-5">
                <!-- 拟签约公司选择 -->
                <div class="form-section fade-in">
                    <h5 class="section-title">
                        <i class="bi bi-building"></i>
                        拟签约公司选择
                    </h5>
                    <div class="mb-3">
                        <select class="form-select" id="contractCompany" onchange="updateCompanyInfo()">
                            <option value="">请选择拟签约公司</option>
                            <option value="浙江登新科技有限公司">浙江登新科技有限公司</option>
                            <option value="湖州物物通科技有限公司">湖州物物通科技有限公司</option>
                        </select>
                    </div>
                    <div id="companyInfo" class="alert alert-info d-none">
                        <div id="companyDetails"></div>
                    </div>
                </div>

                <!-- 客户信息识别 -->
                <div class="form-section fade-in">
                    <h5 class="section-title">
                        <i class="bi bi-person-badge"></i>
                        客户信息智能识别
                    </h5>
                    <div class="mb-3">
                        <label class="form-label fw-bold">客户信息粘贴区</label>
                        <textarea class="form-control" id="customerInfo" rows="6" 
                                placeholder="请粘贴客户发送的完整信息，系统将智能识别：&#10;✓ 公司全称（必须包含'公司'字样）&#10;✓ 纳税人识别号（18位数字+字母组合）&#10;✓ 开户银行（包含'银行'或'行'字样）&#10;✓ 银行账户（10-25位数字）&#10;✓ 公司注册地址&#10;✓ 联系电话（手机号或座机号）"></textarea>
                    </div>
                    <button class="btn btn-primary" onclick="parseCustomerInfo()">
                        <i class="bi bi-cpu me-1"></i> 
                        <span class="loading-spinner spinner-border spinner-border-sm me-1"></span>
                        智能识别客户信息
                    </button>
                    <div id="customerResult" class="mt-3"></div>
                </div>

                <!-- 收件人信息识别 -->
                <div class="form-section fade-in">
                    <h5 class="section-title">
                        <i class="bi bi-truck"></i>
                        收件人信息智能识别
                    </h5>
                    <div class="mb-3">
                        <label class="form-label fw-bold">收件人信息粘贴区</label>
                        <textarea class="form-control" id="recipientInfo" rows="4" 
                                placeholder="请粘贴收件人信息，系统将智能识别：&#10;✓ 收件人姓名（2-4个中文字符）&#10;✓ 收件人手机号（11位数字）&#10;✓ 收件人地址（包含省市区县等地址信息）"></textarea>
                    </div>
                    <button class="btn btn-primary" onclick="parseRecipientInfo()">
                        <i class="bi bi-cpu me-1"></i>
                        <span class="loading-spinner spinner-border spinner-border-sm me-1"></span>
                        智能识别收件信息
                    </button>
                    <div id="recipientResult" class="mt-3"></div>
                </div>

                <!-- 产品选择 -->
                <div class="form-section fade-in">
                    <h5 class="section-title">
                        <i class="bi bi-box-seam"></i>
                        产品选择与配置
                    </h5>
                    <div class="mb-3">
                        <div class="row">
                            <div class="col-8">
                                <select class="form-select" id="productSelect">
                                    <option value="">请选择产品</option>
                                </select>
                            </div>
                            <div class="col-4">
                                <button class="btn btn-primary w-100" onclick="addProduct()">
                                    <i class="bi bi-plus-circle me-1"></i> 添加
                                </button>
                            </div>
                        </div>
                    </div>
                    <div id="selectedProducts"></div>
                    <div class="payment-info">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="fw-bold">合同总金额:</span>
                            <span class="total-amount">¥<span id="totalAmount">0</span></span>
                        </div>
                        <div id="paymentMethod"></div>
                    </div>
                </div>
            </div>

            <!-- 右侧合同预览区域 -->
            <div class="col-lg-7">
                <div class="form-section fade-in">
                    <h5 class="section-title">
                        <i class="bi bi-file-text"></i>
                        合同实时预览
                        <div class="ms-auto">
                            <button class="btn btn-sm btn-outline-primary" onclick="refreshPreview()">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                        </div>
                    </h5>
                    <div class="contract-preview" id="contractPreview">
                        <div class="contract-header">
                            <h2 style="color: #0066cc; margin-bottom: 10px;">设备采购合同</h2>
                            <p class="text-muted mb-0">Contract for Equipment Purchase</p>
                        </div>
                        <div class="text-center text-muted">
                            <i class="bi bi-file-text" style="font-size: 4rem; opacity: 0.3;"></i>
                            <h5 class="mt-3">请完善左侧信息</h5>
                            <p>选择拟签约公司并添加产品后，合同内容将在此实时预览</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 历史记录模态框 -->
    <div class="modal fade" id="historyModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-clock-history me-2"></i>
                        历史记录管理
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="historyList"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-danger" onclick="clearHistory()">清空历史</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="产品配置.js"></script>
    <script>
        // 全局变量
        let selectedProducts = [];
        let customerData = {};
        let recipientData = {};
        let contractCompanyData = {};

        // 公司信息配置
        const companyInfo = {
            "浙江登新科技有限公司": {
                address: "浙江省湖州市安吉县天子湖镇现代工业园区天子湖大道18号",
                taxNumber: "91330523MA2B45AR0R",
                phone: "0571-********",
                bank: "中国农业银行安吉城东支行",
                account: "*****************",
                taxRate: "13%增值税",
                logo: "浙江登新科技有限公司.png"
            },
            "湖州物物通科技有限公司": {
                address: "浙江省湖州市安吉县天子湖镇现代工业园区天子湖大道18号1幢3层301室",
                taxNumber: "91330523MA7B2BJR28",
                phone: "0572-5686506",
                bank: "杭州联合农村商业银行股份有限公司安吉绿色支行天子湖分理处",
                account: "***************",
                taxRate: "1%普通电子发票",
                logo: "湖州物物通科技有限公司.png"
            }
        };

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            loadProducts();
            updateContractDate();
            // 添加淡入动画
            setTimeout(() => {
                document.querySelectorAll('.fade-in').forEach((el, index) => {
                    setTimeout(() => {
                        el.style.opacity = '1';
                        el.style.transform = 'translateY(0)';
                    }, index * 100);
                });
            }, 100);
        });

        // 加载产品列表
        function loadProducts() {
            const productSelect = document.getElementById('productSelect');
            const products = ProductUtils.getAllProductNames();

            // 按类别分组显示产品
            const categories = {};
            products.forEach(productName => {
                const product = ProductUtils.getProduct(productName);
                if (!categories[product.category]) {
                    categories[product.category] = [];
                }
                categories[product.category].push({name: productName, product});
            });

            // 创建分组选项
            Object.keys(categories).forEach(category => {
                const optgroup = document.createElement('optgroup');
                optgroup.label = category;

                categories[category].forEach(({name, product}) => {
                    const option = document.createElement('option');
                    option.value = name;
                    option.textContent = `${name} - ¥${product.price.toLocaleString()}`;
                    optgroup.appendChild(option);
                });

                productSelect.appendChild(optgroup);
            });
        }

        // 更新拟签约公司信息
        function updateCompanyInfo() {
            const companySelect = document.getElementById('contractCompany');
            const companyInfoDiv = document.getElementById('companyInfo');
            const companyDetailsDiv = document.getElementById('companyDetails');

            const selectedCompany = companySelect.value;
            if (selectedCompany && companyInfo[selectedCompany]) {
                contractCompanyData = companyInfo[selectedCompany];
                contractCompanyData.name = selectedCompany;

                companyDetailsDiv.innerHTML = `
                    <div class="row">
                        <div class="col-md-6">
                            <strong><i class="bi bi-building me-1"></i>${selectedCompany}</strong><br>
                            <small class="text-muted">
                                <i class="bi bi-geo-alt me-1"></i>地址：${contractCompanyData.address}<br>
                                <i class="bi bi-credit-card me-1"></i>税号：${contractCompanyData.taxNumber}<br>
                                <i class="bi bi-telephone me-1"></i>电话：${contractCompanyData.phone}
                            </small>
                        </div>
                        <div class="col-md-6">
                            <small class="text-muted">
                                <i class="bi bi-bank me-1"></i>开户行：${contractCompanyData.bank}<br>
                                <i class="bi bi-credit-card-2-front me-1"></i>账号：${contractCompanyData.account}<br>
                                <i class="bi bi-percent me-1"></i>税率：${contractCompanyData.taxRate}
                            </small>
                        </div>
                    </div>
                `;
                companyInfoDiv.classList.remove('d-none');
            } else {
                companyInfoDiv.classList.add('d-none');
                contractCompanyData = {};
            }
            updateContractPreview();
        }

        // 显示加载动画
        function showLoading(buttonElement) {
            const spinner = buttonElement.querySelector('.loading-spinner');
            const icon = buttonElement.querySelector('.bi:not(.loading-spinner .bi)');
            if (spinner) spinner.style.display = 'inline-block';
            if (icon) icon.style.display = 'none';
            buttonElement.disabled = true;
        }

        // 隐藏加载动画
        function hideLoading(buttonElement) {
            const spinner = buttonElement.querySelector('.loading-spinner');
            const icon = buttonElement.querySelector('.bi:not(.loading-spinner .bi)');
            if (spinner) spinner.style.display = 'none';
            if (icon) icon.style.display = 'inline-block';
            buttonElement.disabled = false;
        }

        // 解析客户信息
        function parseCustomerInfo() {
            const button = event.target;
            const text = document.getElementById('customerInfo').value;
            const resultDiv = document.getElementById('customerResult');

            if (!text.trim()) {
                showAlert('请先输入客户信息', 'warning');
                return;
            }

            showLoading(button);

            // 模拟处理时间
            setTimeout(() => {
                // 增强的正则表达式匹配
                const patterns = {
                    company: /([^\r\n，,。.\s]*(?:有限责任公司|股份有限公司|有限公司|集团公司|公司)[^\r\n，,。.\s]*)/,
                    taxNumber: /([A-Z0-9]{15,20})/,
                    bank: /([^\r\n，,。.\s]*(?:银行|农商行|信用社|邮储)[^\r\n，,。.\s]*)/,
                    account: /(\d{10,25})/,
                    phone: /(\d{3,4}[-\s]?\d{7,8}|\d{11})/,
                    address: /([^\r\n，,。.]*(?:省|市|区|县|镇|街道|路|号|室|层|楼)[^\r\n，,。.]*)/
                };

                customerData = {};
                for (const [key, pattern] of Object.entries(patterns)) {
                    const match = text.match(pattern);
                    if (match) {
                        customerData[key] = match[1].trim();
                    }
                }

                // 显示识别结果
                const fields = [
                    {key: 'company', label: '公司全称', icon: 'building'},
                    {key: 'taxNumber', label: '纳税人识别号', icon: 'credit-card'},
                    {key: 'bank', label: '开户银行', icon: 'bank'},
                    {key: 'account', label: '银行账户', icon: 'credit-card-2-front'},
                    {key: 'phone', label: '联系电话', icon: 'telephone'},
                    {key: 'address', label: '注册地址', icon: 'geo-alt'}
                ];

                let resultHtml = '<div class="alert alert-success"><h6><i class="bi bi-check-circle me-1"></i>识别结果：</h6>';
                fields.forEach(field => {
                    const value = customerData[field.key];
                    const status = value ? 'success' : 'danger';
                    const statusIcon = value ? 'check-circle' : 'x-circle';
                    resultHtml += `
                        <div class="d-flex align-items-center mb-1">
                            <i class="bi bi-${field.icon} me-2 text-primary"></i>
                            <span class="me-2">${field.label}:</span>
                            <span class="text-${status}">
                                <i class="bi bi-${statusIcon} me-1"></i>
                                ${value || '未识别'}
                            </span>
                        </div>
                    `;
                });
                resultHtml += '</div>';

                resultDiv.innerHTML = resultHtml;
                hideLoading(button);
                updateContractPreview();

                if (Object.keys(customerData).length > 0) {
                    showAlert('客户信息识别完成！', 'success');
                }
            }, 1000);
        }

        // 解析收件人信息
        function parseRecipientInfo() {
            const button = event.target;
            const text = document.getElementById('recipientInfo').value;
            const resultDiv = document.getElementById('recipientResult');

            if (!text.trim()) {
                showAlert('请先输入收件人信息', 'warning');
                return;
            }

            showLoading(button);

            setTimeout(() => {
                const patterns = {
                    name: /([^\d\s\r\n，,。.]{2,4})/,
                    phone: /(\d{11})/,
                    address: /([^\r\n，,。.]*(?:省|市|区|县|镇|街道|路|号|室|层|楼)[^\r\n，,。.]*)/
                };

                recipientData = {};
                for (const [key, pattern] of Object.entries(patterns)) {
                    const match = text.match(pattern);
                    if (match) {
                        recipientData[key] = match[1].trim();
                    }
                }

                const fields = [
                    {key: 'name', label: '收件人姓名', icon: 'person'},
                    {key: 'phone', label: '收件人手机', icon: 'phone'},
                    {key: 'address', label: '收件人地址', icon: 'geo-alt-fill'}
                ];

                let resultHtml = '<div class="alert alert-success"><h6><i class="bi bi-check-circle me-1"></i>识别结果：</h6>';
                fields.forEach(field => {
                    const value = recipientData[field.key];
                    const status = value ? 'success' : 'danger';
                    const statusIcon = value ? 'check-circle' : 'x-circle';
                    resultHtml += `
                        <div class="d-flex align-items-center mb-1">
                            <i class="bi bi-${field.icon} me-2 text-primary"></i>
                            <span class="me-2">${field.label}:</span>
                            <span class="text-${status}">
                                <i class="bi bi-${statusIcon} me-1"></i>
                                ${value || '未识别'}
                            </span>
                        </div>
                    `;
                });
                resultHtml += '</div>';

                resultDiv.innerHTML = resultHtml;
                hideLoading(button);
                updateContractPreview();

                if (Object.keys(recipientData).length > 0) {
                    showAlert('收件人信息识别完成！', 'success');
                }
            }, 800);
        }

        // 添加产品
        function addProduct() {
            const productSelect = document.getElementById('productSelect');
            const productName = productSelect.value;

            if (!productName) {
                showAlert('请先选择产品', 'warning');
                return;
            }

            const product = ProductUtils.getProduct(productName);
            if (!product) {
                showAlert('产品信息获取失败', 'error');
                return;
            }

            // 检查是否已添加
            const existingIndex = selectedProducts.findIndex(p => p.name === productName);
            if (existingIndex >= 0) {
                selectedProducts[existingIndex].quantity += 1;
                showAlert(`${productName} 数量已增加`, 'info');
            } else {
                selectedProducts.push({
                    name: productName,
                    model: product.model,
                    price: product.price,
                    quantity: 1,
                    category: product.category,
                    description: product.description
                });
                showAlert(`已添加 ${productName}`, 'success');
            }

            productSelect.value = '';
            updateProductList();
            updateContractPreview();
        }

        // 更新产品列表显示
        function updateProductList() {
            const container = document.getElementById('selectedProducts');
            container.innerHTML = '';

            if (selectedProducts.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-box" style="font-size: 2rem;"></i>
                        <p class="mt-2">暂未选择产品</p>
                    </div>
                `;
                document.getElementById('totalAmount').textContent = '0';
                document.getElementById('paymentMethod').innerHTML = '';
                return;
            }

            let total = 0;
            selectedProducts.forEach((product, index) => {
                const subtotal = product.price * product.quantity;
                total += subtotal;

                const productDiv = document.createElement('div');
                productDiv.className = 'product-item';
                productDiv.innerHTML = `
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h6 class="mb-1 text-primary">${product.name}</h6>
                            <small class="text-muted d-block">${product.model}</small>
                            <small class="text-muted">${product.category}</small>
                            <div class="mt-1">
                                <span class="badge bg-secondary">单价: ¥${product.price.toLocaleString()}</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label small">数量</label>
                            <div class="input-group input-group-sm">
                                <button class="btn btn-outline-secondary" onclick="changeQuantity(${index}, -1)">
                                    <i class="bi bi-dash"></i>
                                </button>
                                <input type="number" class="form-control text-center" value="${product.quantity}"
                                       onchange="setQuantity(${index}, this.value)" min="1">
                                <button class="btn btn-outline-secondary" onclick="changeQuantity(${index}, 1)">
                                    <i class="bi bi-plus"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-3 text-end">
                            <div class="fw-bold text-success mb-2">¥${subtotal.toLocaleString()}</div>
                            <button class="btn btn-sm btn-outline-danger" onclick="removeProduct(${index})"
                                    title="删除产品">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                `;
                container.appendChild(productDiv);
            });

            // 更新总金额和支付方式
            document.getElementById('totalAmount').textContent = total.toLocaleString();

            const paymentMethod = ProductUtils.getPaymentMethod(total);
            const paymentDiv = document.getElementById('paymentMethod');

            if (paymentMethod === 'installment') {
                const installment = ProductUtils.calculateInstallment(total);
                paymentDiv.innerHTML = `
                    <div class="mt-3">
                        <div class="d-flex align-items-center mb-2">
                            <i class="bi bi-credit-card me-2 text-warning"></i>
                            <strong class="text-warning">分期付款方式</strong>
                        </div>
                        <div class="row text-sm">
                            <div class="col-4 text-center">
                                <div class="border rounded p-2">
                                    <div class="fw-bold text-primary">预付定金</div>
                                    <div class="text-success">¥${installment.deposit.toLocaleString()}</div>
                                    <small class="text-muted">30%</small>
                                </div>
                            </div>
                            <div class="col-4 text-center">
                                <div class="border rounded p-2">
                                    <div class="fw-bold text-primary">发货款</div>
                                    <div class="text-success">¥${installment.shipping.toLocaleString()}</div>
                                    <small class="text-muted">60%</small>
                                </div>
                            </div>
                            <div class="col-4 text-center">
                                <div class="border rounded p-2">
                                    <div class="fw-bold text-primary">收货尾款</div>
                                    <div class="text-success">¥${installment.final.toLocaleString()}</div>
                                    <small class="text-muted">10%</small>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            } else {
                paymentDiv.innerHTML = `
                    <div class="mt-3">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-cash-coin me-2 text-success"></i>
                            <strong class="text-success">全款付款方式</strong>
                        </div>
                        <small class="text-muted">全款到账，款到发货</small>
                    </div>
                `;
            }
        }

        // 修改产品数量
        function changeQuantity(index, delta) {
            selectedProducts[index].quantity = Math.max(1, selectedProducts[index].quantity + delta);
            updateProductList();
            updateContractPreview();
        }

        // 设置产品数量
        function setQuantity(index, value) {
            const quantity = Math.max(1, parseInt(value) || 1);
            selectedProducts[index].quantity = quantity;
            updateProductList();
            updateContractPreview();
        }

        // 删除产品
        function removeProduct(index) {
            const productName = selectedProducts[index].name;
            if (confirm(`确定要删除 ${productName} 吗？`)) {
                selectedProducts.splice(index, 1);
                updateProductList();
                updateContractPreview();
                showAlert(`已删除 ${productName}`, 'info');
            }
        }

        // 更新合同日期
        function updateContractDate() {
            const today = new Date();
            const year = today.getFullYear();
            const month = String(today.getMonth() + 1).padStart(2, '0');
            const day = String(today.getDate()).padStart(2, '0');
            return `${year}年${month}月${day}日`;
        }

        // 数字转中文大写
        function numberToChinese(num) {
            const digits = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
            const units = ['', '拾', '佰', '仟', '万', '拾万', '佰万', '仟万', '亿'];

            if (num === 0) return '零元';

            let result = '';
            let numStr = num.toString();
            let len = numStr.length;

            for (let i = 0; i < len; i++) {
                let digit = parseInt(numStr[i]);
                let unit = units[len - i - 1];

                if (digit !== 0) {
                    result += digits[digit] + unit;
                } else if (result && !result.endsWith('零')) {
                    result += '零';
                }
            }

            return result.replace(/零+$/, '') + '元';
        }

        // 显示提示消息
        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';

            const icons = {
                success: 'check-circle',
                error: 'x-circle',
                warning: 'exclamation-triangle',
                info: 'info-circle'
            };

            alertDiv.innerHTML = `
                <i class="bi bi-${icons[type]} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alertDiv);

            // 自动消失
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }

        // 刷新预览
        function refreshPreview() {
            updateContractPreview();
            showAlert('合同预览已刷新', 'success');
        }

        // 更新合同预览
        function updateContractPreview() {
            const preview = document.getElementById('contractPreview');

            if (!contractCompanyData.name || selectedProducts.length === 0) {
                preview.innerHTML = `
                    <div style="text-align: center; margin-top: 100pt;">
                        <h2 style="color: #0066cc; font-size: 36pt; margin-bottom: 20pt;">购 买 合 同</h2>
                        <div style="color: #666; font-size: 14pt;">
                            <p>请完善左侧信息后，合同内容将在此实时预览</p>
                            <div style="margin-top: 50pt;">
                                <div style="display: inline-block; margin: 20pt; padding: 20pt; border: 1pt dashed #ccc;">
                                    <i class="bi bi-building" style="font-size: 2rem; color: #0066cc;"></i>
                                    <p>选择拟签约公司</p>
                                </div>
                                <div style="display: inline-block; margin: 20pt; padding: 20pt; border: 1pt dashed #ccc;">
                                    <i class="bi bi-box-seam" style="font-size: 2rem; color: #0066cc;"></i>
                                    <p>添加产品设备</p>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                return;
            }

            const total = selectedProducts.reduce((sum, p) => sum + p.price * p.quantity, 0);
            const contractDate = updateContractDate();
            const paymentMethod = ProductUtils.getPaymentMethod(total);
            const contractNumber = `NEODEN-${contractDate.replace(/[年月日]/g, '')}`;

            // 生成产品表格
            let productTableRows = '';
            selectedProducts.forEach((product) => {
                const subtotal = product.price * product.quantity;
                productTableRows += `
                    <tr>
                        <td style="text-align: center;">${product.name}</td>
                        <td style="text-align: center;">${product.quantity}</td>
                        <td style="text-align: center;">台</td>
                        <td style="text-align: center;">${product.price.toLocaleString()}</td>
                        <td style="text-align: center;">${subtotal.toLocaleString()}</td>
                        <td>${product.description || '设备安装调试培训指导'}</td>
                    </tr>
                `;
            });

            // 分期付款内容
            let paymentDetails = '';
            if (paymentMethod === 'installment') {
                const installment = ProductUtils.calculateInstallment(total);
                paymentDetails = `
                    <p style="margin-left: 21pt; line-height: 24pt;">
                        在本合同涉及的设备重量超过500kg或合同金额超过10万元可采用分期付款方式：
                    </p>
                    <p style="margin-left: 21pt; line-height: 24pt;">
                        预付定金为：<u>${installment.deposit.toLocaleString()}</u>元，大写：<u>${numberToChinese(installment.deposit)}</u>圆整；收到货款后，设备开始进入生产；
                    </p>
                    <p style="margin-left: 21pt; line-height: 24pt;">
                        发货款为：<u>${installment.shipping.toLocaleString()}</u>元，大写：<u>${numberToChinese(installment.shipping)}</u>圆整；收到货款后，设备进入发货流程；
                    </p>
                    <p style="margin-left: 21pt; line-height: 24pt;">
                        收货尾款为：<u>${installment.final.toLocaleString()}</u>元，大写：<u>${numberToChinese(installment.final)}</u>圆整；设备到货后15日内，支付尾款。
                    </p>
                `;
            } else {
                paymentDetails = `
                    <p style="margin-left: 21pt; line-height: 24pt;">
                        全款到账，款到发货
                    </p>
                `;
            }

            preview.innerHTML = `
                <!-- 合同标题 -->
                <div style="text-align: center; margin-bottom: 30pt;">
                    <h1 style="font-size: 36pt; font-weight: bold; margin-bottom: 20pt;">购 买 合 同</h1>
                </div>

                <!-- 基本信息表格 -->
                <table class="contract-table" style="margin-bottom: 30pt; border: none;">
                    <tr>
                        <td style="width: 85.5pt; border: none; border-bottom: 1pt dashed #000;">产品：</td>
                        <td style="width: 319.5pt; border: none; border-bottom: 1pt dashed #000;">NEODEN-贴片设备</td>
                    </tr>
                    <tr>
                        <td style="border: none; border-bottom: 1pt dashed #000;">购买单位：</td>
                        <td style="border: none; border-bottom: 1pt dashed #000;">${customerData.company || '[客户公司名称]'}</td>
                    </tr>
                    <tr>
                        <td style="border: none; border-bottom: 1pt dashed #000;">联系人：</td>
                        <td style="border: none; border-bottom: 1pt dashed #000;">${recipientData.name || '[收件人姓名]'}</td>
                    </tr>
                    <tr>
                        <td style="border: none; border-bottom: 1pt dashed #000;">联系电话：</td>
                        <td style="border: none; border-bottom: 1pt dashed #000;">${recipientData.phone || '[收件人手机号]'}</td>
                    </tr>
                    <tr>
                        <td style="border: none; border-bottom: 1pt dashed #000;">客户地址：</td>
                        <td style="border: none; border-bottom: 1pt dashed #000;">${recipientData.address || '[收件人地址]'}</td>
                    </tr>
                    <tr>
                        <td style="border: none; border-bottom: 1pt dashed #000;">合同号：</td>
                        <td style="border: none; border-bottom: 1pt dashed #000;">${contractNumber}</td>
                    </tr>
                    <tr>
                        <td style="border: none;">日期：</td>
                        <td style="border: none; border-bottom: 1pt dashed #000;">${contractDate}</td>
                    </tr>
                </table>

                <!-- 甲乙双方信息 -->
                <table class="contract-table" style="margin-bottom: 20pt;">
                    <tr>
                        <td style="width: 78.85pt;">甲方（需方）：</td>
                        <td style="width: 204.25pt;">${customerData.company || '[客户公司名称]'}</td>
                    </tr>
                    <tr>
                        <td>乙方（供方）：</td>
                        <td>${contractCompanyData.name}</td>
                    </tr>
                </table>

                <p style="line-height: 24pt; text-indent: 21pt;">
                    甲、乙双方经友好协商，根据《中华人民共和国合同法》等双方共同遵守、执行的原则，协商如下：
                </p>

                <!-- 一、标的物 -->
                <p style="line-height: 24pt; font-weight: bold;">一、标的物</p>
                <table class="contract-table">
                    <tr>
                        <th style="width: 127.35pt;">产品/服务名称</th>
                        <th style="width: 36.75pt;">数量</th>
                        <th style="width: 36.75pt;">单位</th>
                        <th style="width: 60pt;">单价（元）</th>
                        <th style="width: 55.5pt;">总价（元）</th>
                        <th style="width: 148.65pt;">备注</th>
                    </tr>
                    ${productTableRows}
                    <tr>
                        <td colspan="4" style="text-align: right; font-weight: bold;">总计</td>
                        <td style="text-align: center; font-weight: bold;">${total.toLocaleString()}</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td colspan="6" style="text-align: right; font-size: 7.5pt;">
                            以上价格含：☐13%增值税 ☐1%普通电子发票
                        </td>
                    </tr>
                </table>

                <!-- 二、货款及支付 -->
                <p style="line-height: 24pt; font-weight: bold; margin-top: 15pt;">二、货款及支付</p>
                <p style="margin-left: 39pt; text-indent: -18pt; line-height: 24pt;">
                    1. 总货款：${total.toLocaleString()}元，大写：${numberToChinese(total)}圆整
                </p>
                <p style="margin-left: 39pt; text-indent: -18pt; line-height: 24pt;">
                    2. 付款支付：
                </p>
                ${paymentDetails}
                <p style="margin-left: 39pt; text-indent: -18pt; line-height: 24pt;">
                    3. 付款方式：☐现金 ☑转账
                </p>
                <p style="margin-left: 39pt; text-indent: -18pt; line-height: 24pt;">
                    4. 甲方应在合同签订后 <u>3</u> 个工作日内付款
                </p>

                <!-- 三、产品的交付 -->
                <p style="line-height: 24pt; font-weight: bold; margin-top: 15pt;">三、产品的交付</p>
                <p style="margin-left: 39pt; text-indent: -18pt; line-height: 24pt;">
                    1. 交付期：<u>7</u> 个工作日内乙方全部交付完毕
                </p>
                <p style="margin-left: 39pt; text-indent: -18pt; line-height: 24pt;">
                    2. 产品包装：内包装无限制，外包装为木箱。以实际包装为准。
                </p>
                <p style="margin-left: 39pt; text-indent: -18pt; line-height: 24pt;">
                    3. 运输方式：☑物流 ☐自提
                </p>
                <p style="margin-left: 39pt; text-indent: -18pt; line-height: 24pt;">
                    4. 产品交付时，甲方应派 <u>1</u> 名代表到场
                </p>

                <!-- 四、售后服务 -->
                <p style="line-height: 24pt; font-weight: bold; margin-top: 15pt;">四、售后服务</p>
                <p style="margin-left: 39pt; text-indent: -18pt; line-height: 24pt;">
                    1. 产品保修期：12个月（从甲乙双方收到产品验收开始计算）
                </p>
                <p style="text-indent: 36.75pt; line-height: 24pt;">
                    保修期内培训方式：现场培训指导
                </p>
                <p style="margin-left: 39pt; text-indent: -18pt; line-height: 24pt;">
                    2. 产品保修：
                </p>
                <p style="text-indent: 42pt; line-height: 24pt;">
                    2.1 由甲方直接联系公司总部，甲方服务人员由公司直接派遣。
                </p>
                <p style="margin-left: 56.05pt; text-indent: -15.75pt; line-height: 24pt;">
                    2.2 或通过乙方委托的第三方服务，甲方可委托乙方进行产品拆装调试。
                    甲方产品可通过物流到达应由乙方标准。
                </p>
                <p style="margin-left: 39pt; text-indent: -18pt; line-height: 24pt;">
                    3. 售后服务在保修期内，乙方提供技术支持，及时有效解决甲方使用过程中遇到的问题。
                </p>
                <p style="margin-left: 39pt; text-indent: -18pt; line-height: 24pt;">
                    4. 超保修期在保修期内：
                </p>

                <!-- 签名区域 -->
                <div style="margin-top: 50pt; page-break-inside: avoid;">
                    <table style="width: 100%; border: none;">
                        <tr>
                            <td style="width: 50%; border: none; vertical-align: top; padding-right: 20pt;">
                                <p style="font-weight: bold; margin-bottom: 15pt;">甲方（需方）</p>
                                <p>公司：${customerData.company || '[客户公司名称]'}</p>
                                <p>授权代表人：_______________</p>
                                <p>日期：${contractDate}</p>
                                <div style="height: 80pt; border: 1pt solid #ccc; margin-top: 20pt; text-align: center; line-height: 80pt; color: #999;">
                                    甲方签章
                                </div>
                            </td>
                            <td style="width: 50%; border: none; vertical-align: top; padding-left: 20pt;">
                                <p style="font-weight: bold; margin-bottom: 15pt;">乙方（供方）</p>
                                <p>公司：${contractCompanyData.name}</p>
                                <p>授权代表人：_______________</p>
                                <p>日期：${contractDate}</p>
                                <div class="signature-with-seal" style="height: 80pt; margin-top: 20pt; position: relative;">
                                    ${contractCompanyData.logo ?
                                        `<img src="${contractCompanyData.logo}" class="company-logo" style="position: absolute; top: 10pt; left: 50%; transform: translateX(-50%); z-index: 10;">
                                         <div style="height: 80pt; border: 1pt solid #ccc; text-align: center; line-height: 80pt; color: #999; position: relative; z-index: 1;">
                                            乙方签章
                                         </div>` :
                                        `<div style="height: 80pt; border: 1pt solid #ccc; text-align: center; line-height: 80pt; color: #999;">
                                            乙方签章
                                         </div>`
                                    }
                                </div>
                            </td>
                        </tr>
                    </table>
                </div>
            `;
        }

        // 保存合同
        function saveContract() {
            if (selectedProducts.length === 0) {
                showAlert('请先添加产品', 'warning');
                return;
            }

            if (!contractCompanyData.name) {
                showAlert('请先选择拟签约公司', 'warning');
                return;
            }

            const contractData = {
                id: Date.now(),
                date: new Date().toISOString(),
                contractDate: updateContractDate(),
                customerCompany: customerData.company || '未识别客户',
                contractCompany: contractCompanyData.name,
                products: [...selectedProducts],
                customerData: {...customerData},
                recipientData: {...recipientData},
                contractCompanyData: {...contractCompanyData},
                total: selectedProducts.reduce((sum, p) => sum + p.price * p.quantity, 0),
                contractNumber: `NEODEN-${updateContractDate().replace(/[年月日]/g, '')}`
            };

            // 保存到localStorage
            let history = JSON.parse(localStorage.getItem('contractHistory') || '[]');

            // 检查是否已存在相同的合同（基于客户公司和产品）
            const existingIndex = history.findIndex(h =>
                h.customerCompany === contractData.customerCompany &&
                h.contractCompany === contractData.contractCompany &&
                JSON.stringify(h.products) === JSON.stringify(contractData.products)
            );

            if (existingIndex >= 0) {
                // 更新现有记录
                history[existingIndex] = contractData;
                showAlert('合同已更新到历史记录', 'success');
            } else {
                // 添加新记录
                history.unshift(contractData);
                showAlert('合同已保存到历史记录', 'success');
            }

            // 只保留最近50条记录
            if (history.length > 50) {
                history = history.slice(0, 50);
            }

            localStorage.setItem('contractHistory', JSON.stringify(history));
        }

        // 导出PDF功能
        function exportPDF() {
            if (selectedProducts.length === 0) {
                showAlert('请先添加产品', 'warning');
                return;
            }

            // 保存合同到历史记录
            saveContract();

            // 打印预览
            const printWindow = window.open('', '_blank');
            const contractContent = document.getElementById('contractPreview').innerHTML;

            printWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>设备采购合同</title>
                    <style>
                        body { font-family: 'SimSun', serif; margin: 20px; line-height: 1.6; }
                        .contract-header { text-align: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #0066cc; }
                        .table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                        .table th, .table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                        .table th { background-color: #f8f9fa; font-weight: bold; }
                        .text-center { text-align: center; }
                        .text-end { text-align: right; }
                        .fw-bold { font-weight: bold; }
                        .text-primary { color: #0066cc; }
                        .text-danger { color: #dc3545; }
                        .text-muted { color: #6c757d; }
                        .bg-light { background-color: #f8f9fa; }
                        .table-warning { background-color: #fff3cd; }
                        .border { border: 1px solid #ddd; }
                        .rounded { border-radius: 5px; }
                        .p-3 { padding: 15px; }
                        .mb-2 { margin-bottom: 10px; }
                        .mb-3 { margin-bottom: 15px; }
                        .mb-4 { margin-bottom: 20px; }
                        .mt-3 { margin-top: 15px; }
                        .row { display: flex; }
                        .col-6 { width: 50%; padding: 0 10px; }
                        .signature-section { margin-top: 50px; padding-top: 30px; border-top: 1px solid #dee2e6; }
                        @media print {
                            body { margin: 0; }
                            .no-print { display: none; }
                        }
                    </style>
                </head>
                <body>
                    ${contractContent}
                    <div class="no-print" style="text-align: center; margin-top: 30px;">
                        <button onclick="window.print()" style="padding: 10px 20px; background: #0066cc; color: white; border: none; border-radius: 5px; cursor: pointer;">打印合同</button>
                        <button onclick="window.close()" style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer; margin-left: 10px;">关闭</button>
                    </div>
                </body>
                </html>
            `);

            printWindow.document.close();
            showAlert('合同预览窗口已打开，可进行打印或保存为PDF', 'success');
        }

        // 加载历史记录
        function loadHistory() {
            const history = JSON.parse(localStorage.getItem('contractHistory') || '[]');
            const historyList = document.getElementById('historyList');

            if (history.length === 0) {
                historyList.innerHTML = `
                    <div class="text-center text-muted py-5">
                        <i class="bi bi-inbox" style="font-size: 4rem; opacity: 0.3;"></i>
                        <h5 class="mt-3">暂无历史记录</h5>
                        <p>生成的合同将自动保存在这里</p>
                        <small class="text-muted">提示：完善合同信息后点击"保存合同"按钮</small>
                    </div>
                `;
            } else {
                let historyHtml = '<div class="row">';
                history.forEach((contract, index) => {
                    const date = new Date(contract.date).toLocaleString('zh-CN');
                    const contractDate = contract.contractDate || '未知日期';
                    const contractNumber = contract.contractNumber || `NEODEN-${date.replace(/[^\d]/g, '').slice(0, 8)}`;

                    historyHtml += `
                        <div class="col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-header d-flex justify-content-between align-items-center bg-light">
                                    <h6 class="mb-0 text-primary">${contract.customerCompany}</h6>
                                    <small class="text-muted">${date}</small>
                                </div>
                                <div class="card-body">
                                    <div class="mb-2">
                                        <small class="text-muted">合同编号：</small>
                                        <span class="badge bg-secondary">${contractNumber}</span>
                                    </div>
                                    <p class="mb-1">
                                        <i class="bi bi-building me-1 text-primary"></i>
                                        <strong>签约公司:</strong> ${contract.contractCompany}
                                    </p>
                                    <p class="mb-1">
                                        <i class="bi bi-box-seam me-1 text-success"></i>
                                        <strong>产品数量:</strong> ${contract.products.length} 种
                                    </p>
                                    <p class="mb-1">
                                        <i class="bi bi-calendar me-1 text-info"></i>
                                        <strong>合同日期:</strong> ${contractDate}
                                    </p>
                                    <p class="mb-3">
                                        <i class="bi bi-currency-dollar me-1 text-warning"></i>
                                        <strong>合同金额:</strong>
                                        <span class="text-danger fw-bold">¥${contract.total.toLocaleString()}</span>
                                    </p>
                                    <div class="d-grid gap-2">
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-primary" onclick="loadContractData(${index})" title="加载到当前编辑器">
                                                <i class="bi bi-arrow-clockwise me-1"></i>加载
                                            </button>
                                            <button class="btn btn-sm btn-success" onclick="exportHistoryContract(${index})" title="导出为PDF">
                                                <i class="bi bi-file-pdf me-1"></i>导出
                                            </button>
                                            <button class="btn btn-sm btn-info" onclick="generateHistoryDeliveryNote(${index})" title="生成送货单">
                                                <i class="bi bi-truck me-1"></i>送货单
                                            </button>
                                        </div>
                                        <button class="btn btn-sm btn-outline-danger" onclick="deleteHistoryContract(${index})" title="删除此记录">
                                            <i class="bi bi-trash me-1"></i>删除记录
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                });
                historyHtml += '</div>';
                historyList.innerHTML = historyHtml;
            }

            const historyModal = new bootstrap.Modal(document.getElementById('historyModal'));
            historyModal.show();
        }

        // 加载历史合同数据
        function loadContractData(index) {
            const history = JSON.parse(localStorage.getItem('contractHistory') || '[]');
            const contract = history[index];

            if (!contract) {
                showAlert('历史记录不存在', 'error');
                return;
            }

            // 恢复数据
            customerData = contract.customerData || {};
            recipientData = contract.recipientData || {};
            selectedProducts = [...(contract.products || [])];
            contractCompanyData = contract.contractCompanyData || {};

            // 更新界面
            document.getElementById('contractCompany').value = contract.contractCompany || '';
            updateCompanyInfo();

            // 恢复客户信息输入框
            if (contract.customerData && Object.keys(contract.customerData).length > 0) {
                const customerInfo = [
                    contract.customerData.company || '',
                    contract.customerData.taxNumber || '',
                    contract.customerData.bank || '',
                    contract.customerData.account || '',
                    contract.customerData.phone || '',
                    contract.customerData.address || ''
                ].filter(item => item).join('\n');

                document.getElementById('customerInfo').value = customerInfo;

                const resultDiv = document.getElementById('customerResult');
                resultDiv.innerHTML = `
                    <div class="alert alert-success">
                        <h6><i class="bi bi-check-circle me-1"></i>已从历史记录加载客户信息：</h6>
                        <small>公司：${contract.customerData.company || '未知'}</small><br>
                        <small>税号：${contract.customerData.taxNumber || '未知'}</small><br>
                        <small>电话：${contract.customerData.phone || '未知'}</small>
                    </div>
                `;
            }

            // 恢复收件人信息输入框
            if (contract.recipientData && Object.keys(contract.recipientData).length > 0) {
                const recipientInfo = [
                    contract.recipientData.name || '',
                    contract.recipientData.phone || '',
                    contract.recipientData.address || ''
                ].filter(item => item).join('\n');

                document.getElementById('recipientInfo').value = recipientInfo;

                const resultDiv = document.getElementById('recipientResult');
                resultDiv.innerHTML = `
                    <div class="alert alert-success">
                        <h6><i class="bi bi-check-circle me-1"></i>已从历史记录加载收件人信息：</h6>
                        <small>姓名：${contract.recipientData.name || '未知'}</small><br>
                        <small>电话：${contract.recipientData.phone || '未知'}</small><br>
                        <small>地址：${contract.recipientData.address || '未知'}</small>
                    </div>
                `;
            }

            updateProductList();
            updateContractPreview();

            // 关闭模态框
            const historyModal = bootstrap.Modal.getInstance(document.getElementById('historyModal'));
            if (historyModal) {
                historyModal.hide();
            }

            showAlert(`已加载历史合同：${contract.customerCompany}`, 'success');
        }

        // 导出历史合同
        function exportHistoryContract(index) {
            const history = JSON.parse(localStorage.getItem('contractHistory') || '[]');
            const contract = history[index];

            if (!contract) return;

            // 临时加载数据并导出
            const tempCustomerData = customerData;
            const tempRecipientData = recipientData;
            const tempSelectedProducts = selectedProducts;
            const tempContractCompanyData = contractCompanyData;

            customerData = contract.customerData || {};
            recipientData = contract.recipientData || {};
            selectedProducts = contract.products || [];
            contractCompanyData = companyInfo[contract.contractCompany] || {};
            contractCompanyData.name = contract.contractCompany;

            updateContractPreview();
            exportPDF();

            // 恢复原数据
            customerData = tempCustomerData;
            recipientData = tempRecipientData;
            selectedProducts = tempSelectedProducts;
            contractCompanyData = tempContractCompanyData;

            updateContractPreview();
        }

        // 删除历史合同
        function deleteHistoryContract(index) {
            if (!confirm('确定要删除这条历史记录吗？')) return;

            let history = JSON.parse(localStorage.getItem('contractHistory') || '[]');
            history.splice(index, 1);
            localStorage.setItem('contractHistory', JSON.stringify(history));

            loadHistory(); // 重新加载历史记录
            showAlert('历史记录已删除', 'info');
        }

        // 清空历史记录
        function clearHistory() {
            if (!confirm('确定要清空所有历史记录吗？此操作不可恢复！')) return;

            localStorage.removeItem('contractHistory');
            loadHistory(); // 重新加载历史记录
            showAlert('历史记录已清空', 'info');
        }

        // 生成送货单
        function generateDeliveryNote() {
            if (selectedProducts.length === 0) {
                showAlert('请先添加产品', 'warning');
                return;
            }

            if (!customerData.company) {
                showAlert('请先识别客户信息', 'warning');
                return;
            }

            const deliveryDate = updateContractDate();
            const contractNumber = `NEODEN-${deliveryDate.replace(/[年月日]/g, '')}`;

            // 生成产品表格
            let productRows = '';
            selectedProducts.forEach((product, index) => {
                productRows += `
                    <tr>
                        <td style="text-align: center; border: 1pt solid #000; padding: 5pt;">${index + 1}</td>
                        <td style="border: 1pt solid #000; padding: 5pt;">${product.name}</td>
                        <td style="text-align: center; border: 1pt solid #000; padding: 5pt;">${product.quantity}</td>
                        <td style="text-align: center; border: 1pt solid #000; padding: 5pt;">台</td>
                        <td style="border: 1pt solid #000; padding: 5pt;">${product.description || '设备完好'}</td>
                    </tr>
                `;
            });

            const deliveryContent = `
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>送货单</title>
                    <style>
                        body { font-family: '微软雅黑', 'SimSun', serif; margin: 20pt; line-height: 1.5; }
                        .header { text-align: center; margin-bottom: 30pt; border-bottom: 1pt solid #000; padding-bottom: 10pt; }
                        .info-table { width: 100%; margin-bottom: 20pt; }
                        .info-table td { padding: 8pt; border: none; }
                        .product-table { width: 100%; border-collapse: collapse; margin-bottom: 30pt; }
                        .product-table th, .product-table td { border: 1pt solid #000; padding: 8pt; text-align: left; }
                        .product-table th { background-color: #f0f0f0; text-align: center; font-weight: bold; }
                        .signature-area { margin-top: 50pt; }
                        .signature-box { display: inline-block; width: 200pt; height: 80pt; border: 1pt solid #000; margin: 10pt; text-align: center; line-height: 80pt; }
                        @media print {
                            body { margin: 0; }
                            .no-print { display: none; }
                        }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1 style="font-size: 18pt; margin: 0;">送货单</h1>
                    </div>

                    <table class="info-table">
                        <tr>
                            <td style="width: 25%;">电话：15958897021</td>
                            <td style="width: 25%;">联系人：陈家财</td>
                            <td style="width: 25%;">送货日期：</td>
                            <td style="width: 25%;">${deliveryDate}</td>
                        </tr>
                        <tr>
                            <td>送货方式：</td>
                            <td>顺丰快递</td>
                            <td>单号：</td>
                            <td></td>
                        </tr>
                        <tr>
                            <td colspan="4">
                                <strong>送货单位：${contractCompanyData.name}</strong><br>
                                <strong>收货单位：${customerData.company}</strong>
                            </td>
                        </tr>
                        <tr>
                            <td>合同号：${contractNumber}</td>
                            <td colspan="3"></td>
                        </tr>
                    </table>

                    <table class="product-table">
                        <thead>
                            <tr>
                                <th style="width: 8%;">序号</th>
                                <th style="width: 40%;">产品名称</th>
                                <th style="width: 12%;">数量</th>
                                <th style="width: 12%;">单位</th>
                                <th style="width: 28%;">备注</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${productRows}
                        </tbody>
                    </table>

                    <div class="signature-area">
                        <p><strong>收货人签字：</strong>_______________ <strong>日期：</strong>_______________</p>
                        <p><strong>送货人签字：</strong>_______________ <strong>日期：</strong>_______________</p>
                        <p style="margin-top: 30pt;"><strong>备注：</strong>请收货人验收后签字确认，如有问题请及时联系。</p>
                    </div>

                    <div class="no-print" style="text-align: center; margin-top: 30pt;">
                        <button onclick="window.print()" style="padding: 10pt 20pt; background: #0066cc; color: white; border: none; border-radius: 5pt; cursor: pointer; margin-right: 10pt;">打印送货单</button>
                        <button onclick="window.close()" style="padding: 10pt 20pt; background: #6c757d; color: white; border: none; border-radius: 5pt; cursor: pointer;">关闭</button>
                    </div>
                </body>
                </html>
            `;

            const deliveryWindow = window.open('', '_blank');
            deliveryWindow.document.write(deliveryContent);
            deliveryWindow.document.close();

            showAlert('送货单已生成，可进行打印', 'success');
        }

        // 从历史记录生成送货单
        function generateHistoryDeliveryNote(index) {
            const history = JSON.parse(localStorage.getItem('contractHistory') || '[]');
            const contract = history[index];

            if (!contract) return;

            const deliveryDate = updateContractDate();
            const contractNumber = contract.contractNumber || `NEODEN-${contract.contractDate?.replace(/[年月日]/g, '') || ''}`;

            // 生成产品表格
            let productRows = '';
            contract.products.forEach((product, idx) => {
                productRows += `
                    <tr>
                        <td style="text-align: center; border: 1pt solid #000; padding: 5pt;">${idx + 1}</td>
                        <td style="border: 1pt solid #000; padding: 5pt;">${product.name}</td>
                        <td style="text-align: center; border: 1pt solid #000; padding: 5pt;">${product.quantity}</td>
                        <td style="text-align: center; border: 1pt solid #000; padding: 5pt;">台</td>
                        <td style="border: 1pt solid #000; padding: 5pt;">${product.description || '设备完好'}</td>
                    </tr>
                `;
            });

            const deliveryContent = `
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>送货单 - ${contract.customerCompany}</title>
                    <style>
                        body { font-family: '微软雅黑', 'SimSun', serif; margin: 20pt; line-height: 1.5; }
                        .header { text-align: center; margin-bottom: 30pt; border-bottom: 1pt solid #000; padding-bottom: 10pt; }
                        .info-table { width: 100%; margin-bottom: 20pt; }
                        .info-table td { padding: 8pt; border: none; }
                        .product-table { width: 100%; border-collapse: collapse; margin-bottom: 30pt; }
                        .product-table th, .product-table td { border: 1pt solid #000; padding: 8pt; text-align: left; }
                        .product-table th { background-color: #f0f0f0; text-align: center; font-weight: bold; }
                        .signature-area { margin-top: 50pt; }
                        @media print {
                            body { margin: 0; }
                            .no-print { display: none; }
                        }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1 style="font-size: 18pt; margin: 0;">送货单</h1>
                    </div>

                    <table class="info-table">
                        <tr>
                            <td style="width: 25%;">电话：15958897021</td>
                            <td style="width: 25%;">联系人：陈家财</td>
                            <td style="width: 25%;">送货日期：</td>
                            <td style="width: 25%;">${deliveryDate}</td>
                        </tr>
                        <tr>
                            <td>送货方式：</td>
                            <td>顺丰快递</td>
                            <td>单号：</td>
                            <td></td>
                        </tr>
                        <tr>
                            <td colspan="4">
                                <strong>送货单位：${contract.contractCompany}</strong><br>
                                <strong>收货单位：${contract.customerCompany}</strong>
                            </td>
                        </tr>
                        <tr>
                            <td>合同号：${contractNumber}</td>
                            <td colspan="3">原合同日期：${contract.contractDate || '未知'}</td>
                        </tr>
                    </table>

                    <table class="product-table">
                        <thead>
                            <tr>
                                <th style="width: 8%;">序号</th>
                                <th style="width: 40%;">产品名称</th>
                                <th style="width: 12%;">数量</th>
                                <th style="width: 12%;">单位</th>
                                <th style="width: 28%;">备注</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${productRows}
                        </tbody>
                    </table>

                    <div class="signature-area">
                        <p><strong>收货人签字：</strong>_______________ <strong>日期：</strong>_______________</p>
                        <p><strong>送货人签字：</strong>_______________ <strong>日期：</strong>_______________</p>
                        <p style="margin-top: 30pt;"><strong>备注：</strong>请收货人验收后签字确认，如有问题请及时联系。</p>
                        <p><strong>客户联系人：</strong>${contract.recipientData?.name || '未知'}
                           <strong>电话：</strong>${contract.recipientData?.phone || '未知'}</p>
                    </div>

                    <div class="no-print" style="text-align: center; margin-top: 30pt;">
                        <button onclick="window.print()" style="padding: 10pt 20pt; background: #0066cc; color: white; border: none; border-radius: 5pt; cursor: pointer; margin-right: 10pt;">打印送货单</button>
                        <button onclick="window.close()" style="padding: 10pt 20pt; background: #6c757d; color: white; border: none; border-radius: 5pt; cursor: pointer;">关闭</button>
                    </div>
                </body>
                </html>
            `;

            const deliveryWindow = window.open('', '_blank');
            deliveryWindow.document.write(deliveryContent);
            deliveryWindow.document.close();

            showAlert('历史合同送货单已生成', 'success');
        }
    </script>
</body>
</html>
