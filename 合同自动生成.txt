我需要制作一个合同自动生成器；网页版本。
1、我将提供你一个合同模板，在目录下名字为，模板.html，合同将会被实时预览到
2、模板中有非常多的占位符包裹的信息，这些信息内容我罗列如下：
公司全称；纳税人识别号；开户银行；银行账户；公司注册地址；联系电话；
收件人姓名；收件人手机号；收件人地址
拟签约时间
总金额
总金额大写
30%
30%大写
60%
60%大写
10%
10%大写
拟签约公司；拟签约公司纳税人识别号；拟签约公司开户银行；拟签约公司银行账户；拟签约公司公司注册地址；拟签约公司联系电话；
支付方式

3、我分别对上述被占位符包裹的信息进行解释
公司全称；纳税人识别号；开户银行；银行账户；公司注册地址；联系电话；这些是指需要和我签订公司的甲方（需方）的信息，我希望能够设计一个对话框，我把客户发我的信息，直接复制黏贴其中，就可以自动识别并填充。避免识别错误，我告知你一下上述信息的特征。
公司全称，一般都会有公司两个字，比如某某有限责任公司，或者是股份有限公司等等
纳税人识别号，一般由数字+英文字母组成，一般来说是18位
开户银行，一般是某某银行，带银行或者行字
银行账户，一般是数字
联系电话，有可能是座机号码，也有可能是手机号码，手机号码的话是11位，座机号码通常会有区号+7位或8位的数字，有些单位也可能不写区号

收件人姓名；收件人手机号；收件人地址。我希望能够设计一个对话框，我把客户发我的信息，直接复制黏贴其中，就可以自动识别并填充。避免识别错误，我告知你一下上述信息的特征。
收件人姓名，一般是2个字或者3个字也有的可能是4个字，需要将百家姓和中国人取名的规则纳入到识别系统中，避免出错。
收件人手机号，一般是11位数字
收件人地址，某某省某某市某某镇、街道、门牌号等等信息构成。需要将我国的地址纳入到识别系统中，避免出错
另外注意，习惯上，收件人姓名、手机号、收件地址，都是单独出现的，比如收件人的姓名，不可能出现在地址的文件中间，所以请注意识别的逻辑。并且，很多情况下，姓名、手机号、地址中间会有分隔符比如空格或者回车换行

拟签约时间，一般是系统日期，年月日。

总金额，是指所有产品的总价
总金额大写，是指总金额的大写，比如：壹佰元
30%，是指总金额的30%，比如总金额是100元，那么30%就是30元
30%大写，是指总金额的30%的大写
60%，是指总金额的60%
60%大写，是指总金额的60%的大写
10%，是指总金额的10%
10%大写，是指总金额的10%的大写
同时，需要清楚的是，只有选择第二种支付方式时，这些带%的比例数值，才会被应用。

拟签约公司；拟签约公司纳税人识别号；拟签约公司开户银行；拟签约公司银行账户；拟签约公司公司注册地址；拟签约公司联系电话；这些信息，我将预先提供给你。因为拟签约公司是可选的，分别是浙江登新科技有限公司和湖州物物通科技有限公司。未来，网页中，拟签约公司可以被选择。
现在我提供这两家的信息如下：
浙江登新科技有限公司
地址：浙江省湖州市安吉县天子湖镇现代工业园区天子湖大道18号
税号：91330523MA2B45AR0R
电话：0571-28885633
开户：中国农业银行安吉城东支行
帐号：19136001040002286

湖州物物通科技有限公司 
地址：浙江省湖州市安吉县天子湖镇现代工业园区天子湖大道18号1幢3层301室
税号：91330523MA7B2BJR28
电话：0572-5686506
开户银行：杭州联合农村商业银行股份有限公司安吉绿色支行天子湖分理处
银行帐号：201000289072919

支付方式
我这边提供了两种支付方式。我要求这两种支付方式是自动被选择的。我将告诉你条件。
当总金额超过100000元时，自动选择第二种支付方式
当总金额小于100000元时，自动选择第一种支付方式

第二种支付方式是：
预付定金为：{{30%}}元，大写：{{30%大写}}圆整；收到货款后，设备开始进入生产；
发货款为：{{60%}} 元，大写：{{60%大写}} 圆整；收到货款后，设备进入发货流程；
收货尾款为： {{10%}} 元，大写：{{10%大写}}圆整；设备到货后15日内，支付尾款。

第一种支付方式是：
全款到帐，款到发货

4、我在目录下放置了浙江登新科技有限公司和湖州物物通科技有限公司 的印章，印章将被自动印在乙方的授权代表人位置，尺寸是45mm*45mm
5、希望自动印制骑缝章。
6、合同中最重要的是：一、标的物内的内容
我将提供我售卖的产品的名字和单价，数量可以被修改。从而填充到标的物下方的表格中去。自动计算总金额。
7、合同中有“以上价格含 13%增值税 和1%普通电子发票”的选框，当拟签约公司是浙江登新科技有限公司时，勾选13%增值税；当拟签约公司是湖州物物通科技有限公司时，勾选1%普通电子发票。
8、有导出pdf的功能。
9、有历史记录功能，历史数据的保存名字应该是客户的公司全称，并且，在后侧有一个发货单导出功能。发货单模板，我提供在目录下的送货单模板.html
10、当售卖的产品被选择后，在合同的后面就会增加一个对应产品的产品规格书。产品规格书在目录下，目录下有各个产品的规格书，均使用产品名字进行命名的文件夹内
